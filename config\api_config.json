{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "API Configuration", "description": "Configuration for pile analysis API endpoints and settings", "type": "object", "api_endpoints": {"pile_data": {"url": "http://*************/pilediag/pile/pileJson", "method": "POST", "timeout": 30, "retry_attempts": 3, "retry_delay": 2, "headers": {"Content-Type": "application/json", "Accept": "application/json"}}, "result_upload": {"url": "http://*************/pilediag/results/upload", "method": "POST", "timeout": 60, "retry_attempts": 3, "retry_delay": 5, "headers": {"Content-Type": "application/json", "Accept": "application/json"}}}, "authentication": {"type": "none", "api_key": "", "token": "", "username": "", "password": ""}, "data_validation": {"required_fields": ["<PERSON><PERSON>", "Sections", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Diameter", "DetectingDate", "Result_Sections_Data", "Test_Sections_Data"], "optional_fields": ["StrengthGrade", "CastingDate", "TechnicalCode", "Model", "SerialNo", "Certificate", "Tester", "QualificationNo"], "validate_data_structure": true, "validate_data_types": true}, "request_settings": {"max_concurrent_requests": 5, "rate_limit_per_minute": 60, "connection_pool_size": 10, "keep_alive": true}, "error_handling": {"log_errors": true, "raise_on_error": false, "fallback_behavior": "skip", "error_notification": {"enabled": false, "email": "", "webhook": ""}}, "cache_settings": {"enabled": true, "cache_duration_minutes": 30, "cache_directory": "./cache", "max_cache_size_mb": 100}, "logging": {"log_requests": true, "log_responses": false, "log_level": "INFO", "log_file": "./logs/api_client.log"}}