{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Analysis Configuration", "description": "Configuration for GZ traditional analysis integration and processing", "type": "object", "gz_analysis": {"script_path": "../gz_analysis_gui.py", "module_name": "gz_traditional_analysis", "class_name": "GZTraditionalAnalyzer", "enabled_indicators": {"speed": true, "amplitude": true, "energy": true, "psd": false}, "depth_range": 0.5, "enable_depth_range": true, "auto_save_results": true, "result_formats": ["txt", "json", "csv"]}, "data_processing": {"input_format": "api_json", "output_format": "csv", "data_cleaning": {"remove_null_values": true, "remove_duplicate_sections": true, "validate_numeric_ranges": true, "normalize_units": true}, "format_conversion": {"section_data_mapping": {"SEQ": "sequence", "H": "height", "T": "time", "V": "velocity", "A": "amplitude", "F": "frequency", "P": "psd"}, "pile_info_mapping": {"Pile": "pile_name", "PileLength": "length", "Diameter": "diameter", "DetectingDate": "test_date"}}}, "quality_control": {"data_validation": {"min_sections": 1, "max_sections": 100, "min_points_per_section": 10, "velocity_range": [1000, 6000], "amplitude_range": [0, 100], "frequency_range": [10, 100]}, "analysis_validation": {"require_k_values": true, "require_category_result": true, "validate_section_count": true}}, "batch_processing": {"enabled": true, "max_concurrent_analyses": 3, "batch_size": 10, "progress_reporting": true, "auto_retry_failed": true, "max_retry_attempts": 2}, "result_processing": {"generate_summary": true, "include_detailed_report": true, "export_charts": false, "compress_results": false, "result_naming_pattern": "gz_analysis_{pile_name}_{timestamp}", "timestamp_format": "%Y%m%d_%H%M%S"}, "integration_settings": {"use_gui_mode": false, "headless_analysis": true, "preserve_original_data": true, "cleanup_temp_files": true, "temp_directory": "./temp", "working_directory": "./work"}, "performance": {"memory_limit_mb": 1024, "processing_timeout_seconds": 300, "enable_multiprocessing": false, "max_workers": 4}, "logging": {"log_analysis_steps": true, "log_data_conversion": true, "log_validation_results": true, "log_level": "INFO", "log_file": "./logs/analysis.log"}}