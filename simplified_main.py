#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simplified Pile Analysis System - KBZ Data Export
简化桩基分析系统 - KBZ数据导出

This simplified version connects to the pile analysis API and exports data
in KBZ format similar to KBZ1-9.TXT and KBZ1-9.xlsx files.
"""

import sys
import os
import logging
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from pathlib import Path
from datetime import datetime
import threading

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_acquisition.api_client import PileAPIClient
from src.data_processing.kbz_exporter import KBZExporter
from src.utils.logger import setup_logging
from src.utils.config_manager import ConfigManager


class SimplifiedPileAnalysisApp:
    """简化的桩基分析应用程序"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("桩基数据导出系统 - Pile Data Export System")
        self.root.geometry("600x400")
        
        # 初始化组件
        self.api_client = None
        self.kbz_exporter = None
        self.logger = logging.getLogger(__name__)
        
        # 创建界面
        self.create_widgets()
        
        # 初始化API客户端
        self.initialize_components()
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="桩基数据导出系统", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 桩基ID输入
        ttk.Label(main_frame, text="桩基ID:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.pile_id_var = tk.StringVar(value="512856130")
        self.pile_id_entry = ttk.Entry(main_frame, textvariable=self.pile_id_var, width=20)
        self.pile_id_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.output_dir_var = tk.StringVar(value=os.getcwd())
        self.output_dir_entry = ttk.Entry(main_frame, textvariable=self.output_dir_var, width=40)
        self.output_dir_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        self.browse_button = ttk.Button(main_frame, text="浏览", command=self.browse_output_dir)
        self.browse_button.grid(row=2, column=2, pady=5, padx=(5, 0))
        
        # 导出选项
        options_frame = ttk.LabelFrame(main_frame, text="导出选项", padding="10")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        self.export_txt_var = tk.BooleanVar(value=True)
        self.export_excel_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="导出TXT文件 (类似KBZ1-9.TXT)", 
                       variable=self.export_txt_var).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="导出Excel文件 (类似KBZ1-9.xlsx)", 
                       variable=self.export_excel_var).grid(row=1, column=0, sticky=tk.W)
        
        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)
        
        self.fetch_button = ttk.Button(button_frame, text="获取并导出数据", 
                                      command=self.fetch_and_export_data)
        self.fetch_button.pack(side=tk.LEFT, padx=5)
        
        self.clear_button = ttk.Button(button_frame, text="清除缓存", 
                                      command=self.clear_cache)
        self.clear_button.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="状态信息", padding="10")
        status_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # 创建文本框和滚动条
        self.status_text = tk.Text(status_frame, height=10, width=70)
        scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def initialize_components(self):
        """初始化组件"""
        try:
            self.api_client = PileAPIClient()
            self.kbz_exporter = KBZExporter()
            self.log_status("系统初始化完成")
        except Exception as e:
            self.log_status(f"初始化失败: {str(e)}")
            messagebox.showerror("错误", f"系统初始化失败: {str(e)}")
    
    def browse_output_dir(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(initialdir=self.output_dir_var.get())
        if directory:
            self.output_dir_var.set(directory)
    
    def log_status(self, message):
        """记录状态信息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        
        self.status_text.insert(tk.END, log_message)
        self.status_text.see(tk.END)
        self.root.update_idletasks()
        
        # 同时记录到日志文件
        self.logger.info(message)
    
    def fetch_and_export_data(self):
        """获取并导出数据"""
        pile_id = self.pile_id_var.get().strip()
        if not pile_id:
            messagebox.showerror("错误", "请输入桩基ID")
            return
        
        output_dir = self.output_dir_var.get().strip()
        if not output_dir or not os.path.exists(output_dir):
            messagebox.showerror("错误", "请选择有效的输出目录")
            return
        
        if not (self.export_txt_var.get() or self.export_excel_var.get()):
            messagebox.showerror("错误", "请至少选择一种导出格式")
            return
        
        # 在新线程中执行，避免界面冻结
        thread = threading.Thread(target=self._fetch_and_export_worker, 
                                 args=(pile_id, output_dir))
        thread.daemon = True
        thread.start()
    
    def _fetch_and_export_worker(self, pile_id, output_dir):
        """工作线程：获取并导出数据"""
        try:
            # 禁用按钮
            self.root.after(0, lambda: self.fetch_button.config(state='disabled'))
            
            self.log_status(f"开始获取桩基数据: {pile_id}")
            
            # 获取API数据
            pile_data = self.api_client.get_pile_data(pile_id)
            self.log_status("API数据获取成功")
            
            # 导出文件
            exported_files = []
            
            if self.export_txt_var.get():
                txt_file = self.kbz_exporter.export_to_txt(pile_data, output_dir, pile_id)
                exported_files.append(txt_file)
                self.log_status(f"TXT文件导出成功: {txt_file}")
            
            if self.export_excel_var.get():
                excel_file = self.kbz_exporter.export_to_excel(pile_data, output_dir, pile_id)
                exported_files.append(excel_file)
                self.log_status(f"Excel文件导出成功: {excel_file}")
            
            self.log_status(f"数据导出完成，共导出 {len(exported_files)} 个文件")
            
            # 显示成功消息
            self.root.after(0, lambda: messagebox.showinfo("成功", 
                f"数据导出完成！\n导出文件:\n" + "\n".join(exported_files)))
            
        except Exception as e:
            error_msg = f"导出失败: {str(e)}"
            self.log_status(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
        
        finally:
            # 重新启用按钮
            self.root.after(0, lambda: self.fetch_button.config(state='normal'))
    
    def clear_cache(self):
        """清除缓存"""
        try:
            if self.api_client:
                self.api_client.clear_cache()
            self.log_status("缓存已清除")
            messagebox.showinfo("成功", "缓存已清除")
        except Exception as e:
            error_msg = f"清除缓存失败: {str(e)}"
            self.log_status(error_msg)
            messagebox.showerror("错误", error_msg)
    
    def on_closing(self):
        """关闭应用程序"""
        try:
            if self.api_client:
                self.api_client.close()
        except:
            pass
        self.root.destroy()


def setup_environment():
    """设置运行环境"""
    # 创建必要的目录
    directories = ['logs', 'cache', 'temp', 'work', 'results']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # 设置日志
    setup_logging()


def main():
    """主函数"""
    # 设置环境
    setup_environment()
    
    # 创建GUI应用
    root = tk.Tk()
    app = SimplifiedPileAnalysisApp(root)
    
    # 设置关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    # 启动应用
    root.mainloop()


if __name__ == "__main__":
    main()
