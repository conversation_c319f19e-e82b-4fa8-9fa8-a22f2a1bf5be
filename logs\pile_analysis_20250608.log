2025-06-08 05:45:05 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 05:45:05 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 05:45:05 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 05:45:05 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 05:45:05 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 05:45:05 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 05:45:05 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 05:45:05 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 05:45:05 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 05:45:05 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 05:45:05 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 05:45:05 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 05:45:05 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 05:45:07 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-08 05:45:07 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 05:45:07 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 05:45:07 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 05:45:07 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 05:45:07 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-08 05:45:07 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-08 05:45:07 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 05:45:09 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-08 05:45:09 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 05:45:11 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 05:45:11 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取完成
2025-06-08 05:45:12 [    INFO] src.gui.data_loader_window:1353 - Data preview updated successfully
2025-06-08 05:45:40 [   ERROR] src.gui.data_loader_window:567 - 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 05:47:04 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-08 05:47:04 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 05:47:04 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 05:47:04 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 05:47:04 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 05:47:04 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-08 05:47:04 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-08 05:47:04 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 05:47:05 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 05:47:05 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-08 05:47:07 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 05:47:07 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取完成
2025-06-08 05:47:07 [    INFO] src.gui.data_loader_window:1353 - Data preview updated successfully
2025-06-08 05:50:18 [   ERROR] src.gui.data_loader_window:567 - 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 06:04:46 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 06:04:46 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 06:04:46 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 06:04:46 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 06:04:46 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 06:04:46 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:04:46 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 06:04:46 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:04:46 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:04:46 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:04:46 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:04:46 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 06:04:46 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 06:04:49 [    INFO] src.gui.data_loader_window:2593 - Status: 正在初始化组件...
2025-06-08 06:04:49 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:04:49 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 06:04:49 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:04:49 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:04:49 [    INFO] src.gui.data_loader_window:2593 - Status: 组件初始化完成
2025-06-08 06:04:49 [    INFO] src.gui.data_loader_window:1453 - Data loader components initialized successfully
2025-06-08 06:04:49 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 06:04:50 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 06:04:50 [    INFO] src.gui.data_loader_window:2593 - Status: 正在获取API数据...
2025-06-08 06:04:51 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 06:04:51 [    INFO] src.gui.data_loader_window:2593 - Status: API数据获取完成
2025-06-08 06:04:52 [    INFO] src.gui.data_loader_window:1657 - Data preview updated successfully
2025-06-08 06:05:08 [   ERROR] src.gui.data_loader_window:644 - 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 06:06:45 [    INFO] src.gui.data_loader_window:2593 - Status: 正在初始化组件...
2025-06-08 06:06:45 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:06:45 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 06:06:45 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:06:45 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:06:45 [    INFO] src.gui.data_loader_window:2593 - Status: 组件初始化完成
2025-06-08 06:06:45 [    INFO] src.gui.data_loader_window:1453 - Data loader components initialized successfully
2025-06-08 06:06:45 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 06:41:37 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 06:41:37 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 06:41:37 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 06:41:37 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 06:41:37 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 06:41:38 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:41:38 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 06:41:38 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:41:38 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:41:38 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:41:38 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:41:38 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 06:41:38 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 06:41:40 [    INFO] src.gui.data_loader_window:2618 - Status: 正在初始化组件...
2025-06-08 06:41:40 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:41:40 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 06:41:40 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:41:40 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:41:40 [    INFO] src.gui.data_loader_window:2618 - Status: 组件初始化完成
2025-06-08 06:41:40 [    INFO] src.gui.data_loader_window:1478 - Data loader components initialized successfully
2025-06-08 06:41:40 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 06:41:41 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 06:41:41 [    INFO] src.gui.data_loader_window:2618 - Status: 正在获取API数据...
2025-06-08 06:41:42 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 06:41:42 [    INFO] src.gui.data_loader_window:2618 - Status: API数据获取完成
2025-06-08 06:41:42 [    INFO] src.gui.data_loader_window:1682 - Data preview updated successfully
2025-06-08 06:42:14 [   ERROR] src.gui.data_loader_window:620 - ❌ 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 06:44:38 [    INFO] src.gui.data_loader_window:2618 - Status: 正在初始化组件...
2025-06-08 06:44:38 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:44:38 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 06:44:38 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 06:44:38 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 06:44:38 [    INFO] src.gui.data_loader_window:2618 - Status: 组件初始化完成
2025-06-08 06:44:38 [    INFO] src.gui.data_loader_window:1478 - Data loader components initialized successfully
2025-06-08 06:44:38 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 06:44:39 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 06:44:39 [    INFO] src.gui.data_loader_window:2618 - Status: 正在获取API数据...
2025-06-08 06:44:41 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 06:44:41 [    INFO] src.gui.data_loader_window:2618 - Status: API数据获取完成
2025-06-08 06:44:41 [    INFO] src.gui.data_loader_window:1682 - Data preview updated successfully
2025-06-08 06:44:52 [   ERROR] src.gui.data_loader_window:620 - ❌ 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 06:45:06 [   ERROR] src.gui.data_loader_window:620 - ❌ 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 07:03:37 [   ERROR] src.gui.data_loader_window:620 - ❌ 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 07:03:39 [    INFO] src.gui.data_loader_window:2618 - Status: 正在获取API数据...
2025-06-08 07:03:39 [    INFO] src.data_acquisition.api_client:128 - Using cached data for pile 512856130
2025-06-08 07:03:39 [    INFO] src.gui.data_loader_window:2618 - Status: API数据获取完成
2025-06-08 07:03:39 [    INFO] src.gui.data_loader_window:1682 - Data preview updated successfully
2025-06-08 07:03:41 [   ERROR] src.gui.data_loader_window:620 - ❌ 截面分析失败: 'str' object has no attribute 'get'
2025-06-08 07:03:53 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 07:03:53 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 07:03:53 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 07:03:53 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 07:03:53 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 07:03:54 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:03:54 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 07:03:54 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:03:54 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 07:03:54 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:03:54 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 07:03:54 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 07:03:54 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 07:03:58 [    INFO] src.gui.data_loader_window:2641 - Status: 正在初始化组件...
2025-06-08 07:03:58 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:03:58 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 07:03:58 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:03:58 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 07:03:58 [    INFO] src.gui.data_loader_window:2641 - Status: 组件初始化完成
2025-06-08 07:03:58 [    INFO] src.gui.data_loader_window:1501 - Data loader components initialized successfully
2025-06-08 07:03:58 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 07:03:59 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 07:03:59 [    INFO] src.gui.data_loader_window:2641 - Status: 正在获取API数据...
2025-06-08 07:04:00 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 07:04:00 [    INFO] src.gui.data_loader_window:2641 - Status: API数据获取完成
2025-06-08 07:04:01 [    INFO] src.gui.data_loader_window:1705 - Data preview updated successfully
2025-06-08 07:09:52 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 07:09:52 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 07:09:52 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 07:09:52 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 07:09:52 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 07:09:52 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:09:52 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 07:09:52 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:09:52 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 07:09:52 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:09:52 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 07:09:52 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 07:09:52 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 07:09:54 [    INFO] src.gui.data_loader_window:2687 - Status: 正在初始化组件...
2025-06-08 07:09:54 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:09:54 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 07:09:54 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 07:09:54 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 07:09:54 [    INFO] src.gui.data_loader_window:2687 - Status: 组件初始化完成
2025-06-08 07:09:54 [    INFO] src.gui.data_loader_window:1547 - Data loader components initialized successfully
2025-06-08 07:09:54 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 07:10:01 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 07:10:01 [    INFO] src.gui.data_loader_window:2687 - Status: 正在获取API数据...
2025-06-08 07:10:02 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 07:10:02 [    INFO] src.gui.data_loader_window:2687 - Status: API数据获取完成
2025-06-08 07:10:02 [    INFO] src.gui.data_loader_window:1751 - Data preview updated successfully
2025-06-08 09:36:10 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 09:36:10 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 09:36:10 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 09:36:10 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 09:36:10 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 09:36:10 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 09:36:10 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 09:36:10 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 09:36:10 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 09:36:10 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 09:36:10 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 09:36:10 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 09:36:10 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 09:36:14 [    INFO] src.gui.data_loader_window:2783 - Status: 正在初始化组件...
2025-06-08 09:36:14 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 09:36:14 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 09:36:14 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 09:36:14 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 09:36:14 [    INFO] src.gui.data_loader_window:2783 - Status: 组件初始化完成
2025-06-08 09:36:14 [    INFO] src.gui.data_loader_window:1643 - Data loader components initialized successfully
2025-06-08 09:36:14 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 09:47:10 [    INFO] src.gui.data_loader_window:2783 - Status: 正在初始化组件...
2025-06-08 09:47:10 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 09:47:10 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 09:47:10 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 09:47:10 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 09:47:10 [    INFO] src.gui.data_loader_window:2783 - Status: 组件初始化完成
2025-06-08 09:47:10 [    INFO] src.gui.data_loader_window:1643 - Data loader components initialized successfully
2025-06-08 09:47:10 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 09:47:11 [    INFO] src.gui.data_loader_window:2783 - Status: 正在获取API数据...
2025-06-08 09:47:11 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 09:47:13 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 09:47:13 [    INFO] src.gui.data_loader_window:2783 - Status: API数据获取完成
2025-06-08 09:47:13 [    INFO] src.gui.data_loader_window:1847 - Data preview updated successfully
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:45 - 开始分析桩基数据: 测试桩基_file
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:46 - 桩基数据键: ['Pile', 'PileLength', 'Diameter', 'DetectingDate', 'Sections', 'Result_Sections_Data']
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:50 - 截面数据数量: 2
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:55 - 截面 1: 3 个测点
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 1, 'Depth': 2.0, 'Velocity': 95.0, 'Amplitude': 2.0, 'Energy': 190.0, 'Quality': 'Good'}
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:55 - 截面 2: 3 个测点
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 09:47:27 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 4, 'Depth': 12.0, 'Velocity': 88.0, 'Amplitude': 2.8, 'Energy': 246.4, 'Quality': 'Good'}
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:74 - 深度分析结果数量: 6
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_2.0m: 1 个测量值
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 1, 'velocity': 95.0, 'amplitude': 2.0, 'energy': 190.0, 'psd': 0.042105263157894736, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.021052631578947368}
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_5.0m: 1 个测量值
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 2, 'velocity': 92.0, 'amplitude': 2.2, 'energy': 202.4, 'psd': 0.05260869565217392, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.05434782608695652}
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_8.0m: 1 个测量值
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 3, 'velocity': 89.0, 'amplitude': 2.5, 'energy': 222.5, 'psd': 0.0702247191011236, 'quality': 'Fair', 'temperature': None, 'acoustic_time': 0.0898876404494382}
2025-06-08 09:47:28 [    INFO] src.data_processing.section_analysis:83 - Section analysis completed for pile: 测试桩基_file
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:45 - 开始分析桩基数据: 测试桩基_file
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:46 - 桩基数据键: ['Pile', 'PileLength', 'Diameter', 'DetectingDate', 'Sections', 'Result_Sections_Data']
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:50 - 截面数据数量: 2
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:55 - 截面 1: 3 个测点
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 1, 'Depth': 2.0, 'Velocity': 95.0, 'Amplitude': 2.0, 'Energy': 190.0, 'Quality': 'Good'}
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:55 - 截面 2: 3 个测点
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 4, 'Depth': 12.0, 'Velocity': 88.0, 'Amplitude': 2.8, 'Energy': 246.4, 'Quality': 'Good'}
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:74 - 深度分析结果数量: 6
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_2.0m: 1 个测量值
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 1, 'velocity': 95.0, 'amplitude': 2.0, 'energy': 190.0, 'psd': 0.042105263157894736, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.021052631578947368}
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_5.0m: 1 个测量值
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 2, 'velocity': 92.0, 'amplitude': 2.2, 'energy': 202.4, 'psd': 0.05260869565217392, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.05434782608695652}
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_8.0m: 1 个测量值
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 3, 'velocity': 89.0, 'amplitude': 2.5, 'energy': 222.5, 'psd': 0.0702247191011236, 'quality': 'Fair', 'temperature': None, 'acoustic_time': 0.0898876404494382}
2025-06-08 09:47:42 [    INFO] src.data_processing.section_analysis:83 - Section analysis completed for pile: 测试桩基_file
2025-06-08 10:16:35 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 10:16:35 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 10:16:35 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 10:16:35 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 10:16:35 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 10:16:35 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:35 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 10:16:35 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:35 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 10:16:35 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:35 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 10:16:35 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 10:16:35 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 10:16:50 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-08 10:16:50 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-08 10:16:50 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250608.log
2025-06-08 10:16:50 [    INFO] src.utils.logger:103 - Console output: True
2025-06-08 10:16:50 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-08 10:16:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 10:16:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 10:16:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 10:16:50 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-08 10:16:50 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-08 10:16:52 [    INFO] src.gui.data_loader_window:2823 - Status: 正在初始化组件...
2025-06-08 10:16:52 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:52 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-08 10:16:52 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-08 10:16:52 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-08 10:16:52 [    INFO] src.gui.data_loader_window:2823 - Status: 组件初始化完成
2025-06-08 10:16:52 [    INFO] src.gui.data_loader_window:1674 - Data loader components initialized successfully
2025-06-08 10:16:52 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-08 10:16:53 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-08 10:16:53 [    INFO] src.gui.data_loader_window:2823 - Status: 正在获取API数据...
2025-06-08 10:16:54 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-08 10:16:54 [    INFO] src.gui.data_loader_window:2823 - Status: API数据获取完成
2025-06-08 10:16:55 [    INFO] src.gui.data_loader_window:1887 - Data preview updated successfully
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:45 - 开始分析桩基数据: 测试桩基_file
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:46 - 桩基数据键: ['Pile', 'PileLength', 'Diameter', 'DetectingDate', 'Sections', 'Result_Sections_Data']
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:50 - 截面数据数量: 2
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:55 - 截面 1: 3 个测点
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 1, 'Depth': 2.0, 'Velocity': 95.0, 'Amplitude': 2.0, 'Energy': 190.0, 'Quality': 'Good'}
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:55 - 截面 2: 3 个测点
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 4, 'Depth': 12.0, 'Velocity': 88.0, 'Amplitude': 2.8, 'Energy': 246.4, 'Quality': 'Good'}
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:74 - 深度分析结果数量: 6
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_2.0m: 1 个测量值
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 1, 'velocity': 95.0, 'amplitude': 2.0, 'energy': 190.0, 'psd': 0.042105263157894736, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.021052631578947368}
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_5.0m: 1 个测量值
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 2, 'velocity': 92.0, 'amplitude': 2.2, 'energy': 202.4, 'psd': 0.05260869565217392, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.05434782608695652}
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_8.0m: 1 个测量值
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 3, 'velocity': 89.0, 'amplitude': 2.5, 'energy': 222.5, 'psd': 0.0702247191011236, 'quality': 'Fair', 'temperature': None, 'acoustic_time': 0.0898876404494382}
2025-06-08 10:17:03 [    INFO] src.data_processing.section_analysis:83 - Section analysis completed for pile: 测试桩基_file
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:45 - 开始分析桩基数据: 测试桩基_file
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:46 - 桩基数据键: ['Pile', 'PileLength', 'Diameter', 'DetectingDate', 'Sections', 'Result_Sections_Data']
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:50 - 截面数据数量: 2
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:55 - 截面 1: 3 个测点
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 1, 'Depth': 2.0, 'Velocity': 95.0, 'Amplitude': 2.0, 'Energy': 190.0, 'Quality': 'Good'}
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:55 - 截面 2: 3 个测点
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 4, 'Depth': 12.0, 'Velocity': 88.0, 'Amplitude': 2.8, 'Energy': 246.4, 'Quality': 'Good'}
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:74 - 深度分析结果数量: 6
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_2.0m: 1 个测量值
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 1, 'velocity': 95.0, 'amplitude': 2.0, 'energy': 190.0, 'psd': 0.042105263157894736, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.021052631578947368}
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_5.0m: 1 个测量值
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 2, 'velocity': 92.0, 'amplitude': 2.2, 'energy': 202.4, 'psd': 0.05260869565217392, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.05434782608695652}
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_8.0m: 1 个测量值
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 3, 'velocity': 89.0, 'amplitude': 2.5, 'energy': 222.5, 'psd': 0.0702247191011236, 'quality': 'Fair', 'temperature': None, 'acoustic_time': 0.0898876404494382}
2025-06-08 10:17:11 [    INFO] src.data_processing.section_analysis:83 - Section analysis completed for pile: 测试桩基_file
