#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Enhanced KBZ Export with Complete Data Structure
测试增强的KBZ导出功能（包含完整数据结构）

This script tests the enhanced KBZ exporter with complete data structure
including detailed measurement points and wave data.
"""

import sys
import os
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_processing.kbz_exporter import KBZExporter
from src.utils.logger import setup_logging


def create_complete_sample_data():
    """创建包含完整数据结构的示例数据"""
    return {
        "Pile": "KBZ1-9",
        "Sections": 3,
        "StrengthGrade": "C35",
        "CastingDate": "2024年10月12日",
        "DetectingDate": "2024年12月23日 15:54:26",
        "PileLength": 15.90,
        "Diameter": 1200.0,
        "TechnicalCode": "JGJ 106-2014",
        "Model": "U5700",
        "SerialNo": "U72007005N",
        "Certificate": "",
        "Tester": "",
        "QualificationNo": "",
        "Version": "ZBLU5700 V1.0.0",
        "TotalObjects": 3,
        "Project": "1223",
        "Address": "",
        "DesignUnit": "",
        "ConstructionUnit": "",
        "BuildingUnit": "",
        "SupervisionUnit": "",
        "MonitoringUnit": "",
        "EntrustingUnit": "",
        "DetectionUnit": "",
        "EntrustingDate": "2024年12月23日",
        "CalculationDate": "2024年12月23日",
        
        # Result_Sections_Data - ZpwSectionJson格式
        "Result_Sections_Data": [
            {
                "SectionNo": 0,
                "SectionName": "1-2",
                "InitialDepth": 0.10,
                "Interval": 0.10,
                "PipeDist": 470.0,
                "HeightCorrection": 0.00,
                "Direction": "正向",
                "nDirection": 1,
                "TotalPoints": 158,
                "TestedLength": 15.90,
                "CriticOfPsd": 200.00,
                "CriticalValue": "二倍",
                "Vc1": 3.783,
                "Vc2": 0.000,
                "Ac1": 132.961,
                "Ac2": 0.000,
                "Fc1": 0.000,
                "Fc2": 0.000,
                "Coefficient1": 2.49,
                "Coefficient3": 0.00,
                "Vm": 4.127,
                "Sv": 0.1385,
                "Am": 138.96,
                "Sa": 5.119,
                "Fm": 0.00,
                "Sf": 0.000,
                "CveSpeed": 0.0336,
                "CveAmp": 0.037,
                "fAveTime": 3.783,
                
                # data - ZpwSectionDataJson格式
                "data": [
                    {
                        "SEQ": 1,
                        "H": 0.20,
                        "T": 123.40,
                        "V": 3.809,
                        "A": 115.77,
                        "F": 0.00,
                        "P": 1.600,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "nValidFlag": 1
                    },
                    {
                        "SEQ": 2,
                        "H": 0.30,
                        "T": 127.40,
                        "V": 3.689,
                        "A": 115.42,
                        "F": 0.00,
                        "P": 0.000,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "nValidFlag": 1
                    },
                    {
                        "SEQ": 3,
                        "H": 0.40,
                        "T": 127.40,
                        "V": 3.689,
                        "A": 122.29,
                        "F": 0.00,
                        "P": 7.056,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "nValidFlag": 1
                    }
                ]
            },
            {
                "SectionNo": 1,
                "SectionName": "1-3",
                "InitialDepth": 0.10,
                "Interval": 0.10,
                "PipeDist": 770.0,
                "HeightCorrection": 0.00,
                "Direction": "正向",
                "nDirection": 1,
                "TotalPoints": 158,
                "TestedLength": 15.90,
                "CriticOfPsd": 200.00,
                "CriticalValue": "二倍",
                "Vc1": 4.001,
                "Vc2": 0.000,
                "Ac1": 125.978,
                "Ac2": 0.000,
                "Fc1": 0.000,
                "Fc2": 0.000,
                "Coefficient1": 2.49,
                "Coefficient3": 0.00,
                "Vm": 4.228,
                "Sv": 0.0911,
                "Am": 131.98,
                "Sa": 2.703,
                "Fm": 0.00,
                "Sf": 0.000,
                "CveSpeed": 0.0215,
                "CveAmp": 0.020,
                "fAveTime": 4.001,
                
                "data": [
                    {
                        "SEQ": 1,
                        "H": 0.20,
                        "T": 181.40,
                        "V": 4.245,
                        "A": 124.18,
                        "F": 0.00,
                        "P": 0.576,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "nValidFlag": 1
                    },
                    {
                        "SEQ": 2,
                        "H": 0.30,
                        "T": 179.00,
                        "V": 4.302,
                        "A": 125.55,
                        "F": 0.00,
                        "P": 0.576,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "nValidFlag": 1
                    }
                ]
            },
            {
                "SectionNo": 2,
                "SectionName": "2-3",
                "InitialDepth": 0.10,
                "Interval": 0.10,
                "PipeDist": 800.0,
                "HeightCorrection": 0.00,
                "Direction": "正向",
                "nDirection": 1,
                "TotalPoints": 158,
                "TestedLength": 15.90,
                "CriticOfPsd": 200.00,
                "CriticalValue": "二倍",
                "Vc1": 4.274,
                "Vc2": 0.000,
                "Ac1": 125.596,
                "Ac2": 0.000,
                "Fc1": 0.000,
                "Fc2": 0.000,
                "Coefficient1": 2.49,
                "Coefficient3": 0.00,
                "Vm": 4.500,
                "Sv": 0.0910,
                "Am": 131.60,
                "Sa": 4.837,
                "Fm": 0.00,
                "Sf": 0.000,
                "CveSpeed": 0.0202,
                "CveAmp": 0.037,
                "fAveTime": 4.274,
                
                "data": [
                    {
                        "SEQ": 1,
                        "H": 0.20,
                        "T": 171.10,
                        "V": 4.676,
                        "A": 111.49,
                        "F": 0.00,
                        "P": 0.400,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "nValidFlag": 1
                    }
                ]
            }
        ],
        
        # Test_Sections_Data - ZpwTestDataSectionDataJson格式
        "Test_Sections_Data": [
            {
                "ObjectNo": 0,
                "Object": "1-2",
                "TotalPoints": 158,
                "MaxSubNo": 1,
                "Dist": 470.00,
                "SamplingLength": 1024,
                "SavedLength": 1024,
                "SamplingInterval": 0.40,
                "TrigVoltage": 250,
                "InitialTime": 13.80,
                "TriggerMode": 3,
                "Channel": 0,
                
                # UltraData - ZpwCUltraDataJson格式
                "UltraData": [
                    {
                        "SEQ": 1,
                        "T": 107.80,
                        "orgTime": 107.80,
                        "A": 139.44,
                        "F": 0.00,
                        "fDist": 15.90,
                        "validFlag": 1,
                        "cName": "001-01",
                        "fOrgA": 139.44,
                        "PSD": 0.000,
                        "WaveVelocity": 4.360,
                        "H": 15.90,
                        "Amplification": 69,
                        "Baseline": 0,
                        "Delay": 222,
                        "JudgeFlag": 0,
                        "deleteFlag": 0,
                        "Gain": 69,
                        "Start": 100,
                        "FirstPeak": 150,
                        "CursorX": 200,
                        "CursorY": 300,
                        "BaseCor": 0,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "Integrity": 1.0,
                        "RealDist": 15.90,
                        
                        # WaveData - 波形数据
                        "WaveData": [2014, 2025, 2031, 2014, 2014, 2019, 2021, 2032, 2028, 2018, 2015, 2012, 2018, 2024, 2015, 2021, 2028, 2017, 2010, 2009, 2011, 2020, 2022, 2015, 2013, 2015, 2018, 2018, 2018, 2013, 2019, 2025, 2023, 2023, 2019, 2019, 2025, 2025, 2020, 2030, 2038, 2030, 2024, 2021, 2022, 2033, 2040, 2034, 2020, 2017, 2024, 2034, 2036, 2034, 2028, 2023, 2031, 2035, 2036, 2031, 2028, 2028, 2023, 2028, 2032, 2036, 2035, 2026, 2025, 2033, 2037, 2036, 2036, 2038, 2038, 2040, 2045, 2049, 2045, 2080, 2100, 2043, 1996, 1943, 1869, 1771, 1653, 1516, 1375, 1246, 1120, 1015, 938, 895, 889, 925, 1009, 1142, 1329, 1568, 1853, 2178, 2542, 2928, 3315, 3697, 3982, 4090, 4095, 4095, 4095, 4094, 4094, 4094, 4094, 4095, 4094, 3965, 3679, 3295, 2866, 2413, 1960, 1525, 1119, 749, 422, 157, 18, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 18, 96, 212, 362, 549, 779, 1058, 1364, 1725, 2158, 2621, 3107, 3629, 4004, 4095, 4095, 4095, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4095, 4074, 3824, 3406, 2902, 2336, 1740, 1133, 512, 90, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 27, 246, 590, 1037, 1584, 2199, 2833, 3481, 3952, 4095, 4095, 4095, 4095, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4095, 4095, 3965, 3623, 3218, 2818, 2416, 1991, 1539, 1052, 492, 84]
                    },
                    {
                        "SEQ": 2,
                        "T": 105.40,
                        "orgTime": 105.40,
                        "A": 138.17,
                        "F": 0.00,
                        "fDist": 15.80,
                        "validFlag": 1,
                        "cName": "002-01",
                        "fOrgA": 138.17,
                        "PSD": 0.000,
                        "WaveVelocity": 4.459,
                        "H": 15.80,
                        "Amplification": 69,
                        "Baseline": 0,
                        "Delay": 222,
                        "JudgeFlag": 0,
                        "deleteFlag": 0,
                        "Gain": 69,
                        "Start": 100,
                        "FirstPeak": 150,
                        "CursorX": 200,
                        "CursorY": 300,
                        "BaseCor": 0,
                        "VNormFlag": 0,
                        "ANormFlag": 0,
                        "FNormFlag": 0,
                        "PNormFlag": 0,
                        "FlawFlag": 0,
                        "Integrity": 1.0,
                        "RealDist": 15.80,
                        
                        "WaveData": [2031, 2034, 2027, 2024, 2023, 2021, 2028, 2033, 2027, 2024, 2023, 2020, 2031, 2034, 2033, 2033, 2027, 2024, 2031, 2034, 2035, 2035, 2025, 2024, 2026, 2024, 2031, 2030, 2031, 2025, 2015, 2015, 2006, 2008, 2019, 2023, 2028, 2019, 2015, 2023, 2027, 2027, 2027, 2015, 2021, 2036, 2031, 2024, 2022, 2028, 2039, 2037, 2036, 2036, 2027, 2032, 2038, 2029, 2033, 2039, 2043, 2044, 2035, 2019, 2023, 2026, 2022, 2028, 2033, 2033, 2035, 2035, 2032, 2032, 2023, 2020, 2008, 1966, 1907, 1842, 1763, 1678, 1578, 1465, 1350, 1245, 1167, 1118, 1094, 1052, 1046, 1143, 1263, 1411, 1592, 1797, 2039, 2312, 2601, 2901, 3195, 3464, 3706, 3936, 4073, 4095, 4095, 4095, 4094, 4094, 4095, 4095, 4046, 3900, 3699, 3450, 3149, 2825, 2497, 2156, 1811, 1482, 1169, 868, 589, 333, 123, 14, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 10, 99, 244, 414, 596, 797, 1017, 1252, 1505, 1774, 2052, 2342, 2648, 2960, 3264, 3560, 3858, 4058, 4095, 4095, 4095, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4095, 4083, 3929, 3656, 3313, 2928, 2518, 2098, 1686, 1285, 901, 542, 220, 29, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 21, 169, 375, 608, 865, 1148, 1450, 1761, 2093, 2445, 2805, 3170, 3534, 3869, 4065, 4095, 4095, 4095, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4094, 4095, 4078, 3927, 3690, 3463, 3222, 2934, 2683, 2452, 2210, 1921, 1569, 1154, 671, 236, 24]
                    }
                ]
            }
        ]
    }


def test_enhanced_kbz_export():
    """测试增强的KBZ导出功能"""
    print("Testing Enhanced KBZ Export with Complete Data Structure...")
    print("=" * 60)
    
    try:
        # 设置日志
        setup_logging()
        
        # 创建完整示例数据
        pile_data = create_complete_sample_data()
        print(f"Created sample data for pile: {pile_data['Pile']}")
        
        # 初始化导出器
        exporter = KBZExporter()
        print("✓ KBZ exporter initialized")
        
        # 创建输出目录
        output_dir = "./enhanced_test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 测试TXT导出
        print("\nTesting TXT export...")
        txt_file = exporter.export_to_txt(pile_data, output_dir, "KBZ1-9")
        print(f"✓ TXT file exported: {txt_file}")
        
        # 检查TXT文件内容
        with open(txt_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        print(f"  File size: {len(content)} characters")
        
        # 检查关键内容
        checks = [
            ("基础数据", "基础数据" in content),
            ("声波测试数据", "声波测试数据" in content),
            ("各测点声参量及波形", "各测点声参量及波形" in content),
            ("波形放大倍数", "波形放大倍数" in content),
            ("基线修正值", "基线修正值" in content),
            ("波形首点延迟点数", "波形首点延迟点数" in content),
            ("Wave data present", "2014 2025 2031" in content)
        ]
        
        for check_name, result in checks:
            status = "✓" if result else "✗"
            print(f"  {status} {check_name}")
        
        # 测试Excel导出
        print("\nTesting Excel export...")
        excel_file = exporter.export_to_excel(pile_data, output_dir, "KBZ1-9")
        print(f"✓ Excel file exported: {excel_file}")
        
        # 检查Excel文件
        import pandas as pd
        excel_data = pd.ExcelFile(excel_file)
        print(f"  Sheets: {excel_data.sheet_names}")
        
        # 检查数据表内容
        data_sheet = pd.read_excel(excel_file, sheet_name='数据表')
        print(f"  Data sheet shape: {data_sheet.shape}")
        
        print("\n" + "=" * 60)
        print("Enhanced KBZ Export Test Completed Successfully!")
        print(f"Output files:")
        print(f"  - {txt_file}")
        print(f"  - {excel_file}")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_enhanced_kbz_export()
    sys.exit(0 if success else 1)
