#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Section Data Exporter
截面数据导出器 - 导出详细的截面分析数据
"""

import pandas as pd
import json
import os
from typing import Dict, List, Any
import logging
from datetime import datetime
from .section_analysis import SectionAnalyzer

class SectionDataExporter:
    """截面数据导出器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.analyzer = SectionAnalyzer()
    
    def export_complete_section_data(self, pile_data: Dict[str, Any], output_dir: str = "section_exports") -> Dict[str, str]:
        """
        导出完整的截面数据
        
        Args:
            pile_data: 桩基数据
            output_dir: 输出目录
            
        Returns:
            导出文件路径字典
        """
        try:
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 分析截面数据
            analysis_result = self.analyzer.analyze_pile_sections(pile_data)
            
            pile_name = pile_data.get('<PERSON>le', 'Unknown_Pile')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            exported_files = {}
            
            # 1. 导出截面组合数据 (1-2, 1-3, 2-3等)
            exported_files['section_combinations'] = self._export_section_combinations(
                analysis_result['section_combinations'], 
                output_dir, 
                pile_name, 
                timestamp
            )
            
            # 2. 导出深度数据 (每个深度的波幅、波速、PSD)
            exported_files['depth_data'] = self._export_depth_data(
                analysis_result['depth_analysis'], 
                output_dir, 
                pile_name, 
                timestamp
            )
            
            # 3. 导出临界值数据
            exported_files['critical_values'] = self._export_critical_values(
                analysis_result['critical_values'], 
                output_dir, 
                pile_name, 
                timestamp
            )
            
            # 4. 导出声时vs波幅数据
            exported_files['acoustic_time_vs_amplitude'] = self._export_acoustic_time_data(
                analysis_result['acoustic_time_analysis'], 
                output_dir, 
                pile_name, 
                timestamp
            )
            
            # 5. 导出PSD分析数据
            exported_files['psd_analysis'] = self._export_psd_data(
                analysis_result['psd_analysis'], 
                output_dir, 
                pile_name, 
                timestamp
            )
            
            # 6. 导出完整分析报告
            exported_files['complete_analysis'] = self._export_complete_analysis(
                analysis_result, 
                output_dir, 
                pile_name, 
                timestamp
            )
            
            self.logger.info(f"Section data export completed for pile: {pile_name}")
            return exported_files
            
        except Exception as e:
            self.logger.error(f"Error exporting section data: {str(e)}")
            raise
    
    def _export_section_combinations(self, combinations_data: Dict, output_dir: str, pile_name: str, timestamp: str) -> str:
        """导出截面组合数据"""
        filename = f"{pile_name}_section_combinations_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 汇总表
            summary_data = []
            for combo_name, combo_data in combinations_data.items():
                summary_data.append({
                    'Section_Combination': combo_name,
                    'Section1_No': combo_data['section1_info']['section_no'],
                    'Section1_Name': combo_data['section1_info']['section_name'],
                    'Section1_Depth_Range': f"{combo_data['section1_info']['start_depth']}-{combo_data['section1_info']['end_depth']}m",
                    'Section1_Points': combo_data['section1_info']['points_count'],
                    'Section2_No': combo_data['section2_info']['section_no'],
                    'Section2_Name': combo_data['section2_info']['section_name'],
                    'Section2_Depth_Range': f"{combo_data['section2_info']['start_depth']}-{combo_data['section2_info']['end_depth']}m",
                    'Section2_Points': combo_data['section2_info']['points_count'],
                    'Combined_Depth_Range': f"{combo_data['depth_range']['min_depth']}-{combo_data['depth_range']['max_depth']}m",
                    'Total_Points': combo_data['combined_analysis'].get('total_points', 0)
                })
            
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Combinations_Summary', index=False)
            
            # 每个组合的详细数据
            for combo_name, combo_data in combinations_data.items():
                if 'depth_profile' in combo_data['combined_analysis']:
                    profile_df = pd.DataFrame(combo_data['combined_analysis']['depth_profile'])
                    sheet_name = f"Combo_{combo_name.replace('-', '_')}"[:31]  # Excel sheet name limit
                    profile_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        return filepath
    
    def _export_depth_data(self, depth_data: Dict, output_dir: str, pile_name: str, timestamp: str) -> str:
        """导出深度数据"""
        filename = f"{pile_name}_depth_analysis_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 所有深度数据汇总
            all_depth_data = []
            
            for depth_key, depth_info in depth_data.items():
                depth = depth_info['depth']
                measurements = depth_info['measurements']
                
                for measurement in measurements:
                    all_depth_data.append({
                        'Depth_m': depth,
                        'Section_No': measurement['section_no'],
                        'Point_No': measurement['point_no'],
                        'Velocity_m_s': measurement['velocity'],
                        'Amplitude': measurement['amplitude'],
                        'Energy': measurement['energy'],
                        'PSD': measurement['psd'],
                        'Acoustic_Time_s': measurement['acoustic_time'],
                        'Quality': measurement['quality'],
                        'Temperature_C': measurement.get('temperature', '')
                    })
            
            all_depth_df = pd.DataFrame(all_depth_data)
            all_depth_df = all_depth_df.sort_values(['Depth_m', 'Section_No'])
            all_depth_df.to_excel(writer, sheet_name='All_Depth_Data', index=False)
            
            # 按深度分组的统计数据
            depth_stats = []
            for depth_key, depth_info in depth_data.items():
                if 'statistics' in depth_info:
                    stats = depth_info['statistics']
                    depth_stats.append({
                        'Depth_m': depth_info['depth'],
                        'Measurement_Count': stats.get('measurement_count', 0),
                        'Velocity_Mean': stats.get('velocity_stats', {}).get('mean', ''),
                        'Velocity_Std': stats.get('velocity_stats', {}).get('std', ''),
                        'Amplitude_Mean': stats.get('amplitude_stats', {}).get('mean', ''),
                        'Amplitude_Std': stats.get('amplitude_stats', {}).get('std', ''),
                        'Energy_Mean': stats.get('energy_stats', {}).get('mean', ''),
                        'Energy_Std': stats.get('energy_stats', {}).get('std', ''),
                        'PSD_Mean': stats.get('psd_stats', {}).get('mean', ''),
                        'PSD_Std': stats.get('psd_stats', {}).get('std', '')
                    })
            
            depth_stats_df = pd.DataFrame(depth_stats)
            depth_stats_df = depth_stats_df.sort_values('Depth_m')
            depth_stats_df.to_excel(writer, sheet_name='Depth_Statistics', index=False)
        
        return filepath
    
    def _export_critical_values(self, critical_data: Dict, output_dir: str, pile_name: str, timestamp: str) -> str:
        """导出临界值数据"""
        filename = f"{pile_name}_critical_values_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        critical_values_list = []
        
        for param_type, param_data in critical_data.items():
            if isinstance(param_data, dict) and 'mean' in param_data:
                critical_values_list.append({
                    'Parameter': param_type.replace('_critical', '').title(),
                    'Mean': param_data.get('mean', ''),
                    'Standard_Deviation': param_data.get('std', ''),
                    'Min_Threshold_5th_Percentile': param_data.get('min_threshold', ''),
                    'Max_Threshold_95th_Percentile': param_data.get('max_threshold', ''),
                    'Critical_Low_Mean_minus_2STD': param_data.get('critical_low', ''),
                    'Critical_High_Mean_plus_2STD': param_data.get('critical_high', ''),
                    'Unit': self._get_parameter_unit(param_type)
                })
        
        critical_df = pd.DataFrame(critical_values_list)
        critical_df.to_excel(filepath, index=False)
        
        return filepath
    
    def _export_acoustic_time_data(self, acoustic_data: Dict, output_dir: str, pile_name: str, timestamp: str) -> str:
        """导出声时vs波幅数据"""
        filename = f"{pile_name}_acoustic_time_vs_amplitude_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 整体关系数据
            if 'overall_relationship' in acoustic_data:
                overall_data = acoustic_data['overall_relationship']
                overall_summary = pd.DataFrame([{
                    'Total_Points': overall_data.get('total_points', 0),
                    'Correlation_Coefficient': overall_data.get('correlation_coefficient', ''),
                    'Acoustic_Time_Min_s': overall_data.get('acoustic_time_statistics', {}).get('min', ''),
                    'Acoustic_Time_Max_s': overall_data.get('acoustic_time_statistics', {}).get('max', ''),
                    'Acoustic_Time_Mean_s': overall_data.get('acoustic_time_statistics', {}).get('mean', ''),
                    'Acoustic_Time_Std_s': overall_data.get('acoustic_time_statistics', {}).get('std', ''),
                    'Amplitude_Min': overall_data.get('amplitude_statistics', {}).get('min', ''),
                    'Amplitude_Max': overall_data.get('amplitude_statistics', {}).get('max', ''),
                    'Amplitude_Mean': overall_data.get('amplitude_statistics', {}).get('mean', ''),
                    'Amplitude_Std': overall_data.get('amplitude_statistics', {}).get('std', '')
                }])
                overall_summary.to_excel(writer, sheet_name='Overall_Relationship', index=False)
            
            # 按截面的数据
            if 'by_section' in acoustic_data:
                for section_key, section_data in acoustic_data['by_section'].items():
                    if 'data_points' in section_data:
                        section_df = pd.DataFrame(section_data['data_points'])
                        sheet_name = section_key.replace('section_', 'Section_')
                        section_df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # 截面汇总
                section_summary = []
                for section_key, section_data in acoustic_data['by_section'].items():
                    section_summary.append({
                        'Section': section_key,
                        'Correlation_Coefficient': section_data.get('correlation_coefficient', ''),
                        'Acoustic_Time_Min_s': section_data.get('acoustic_time_range', {}).get('min', ''),
                        'Acoustic_Time_Max_s': section_data.get('acoustic_time_range', {}).get('max', ''),
                        'Acoustic_Time_Mean_s': section_data.get('acoustic_time_range', {}).get('mean', ''),
                        'Amplitude_Min': section_data.get('amplitude_range', {}).get('min', ''),
                        'Amplitude_Max': section_data.get('amplitude_range', {}).get('max', ''),
                        'Amplitude_Mean': section_data.get('amplitude_range', {}).get('mean', '')
                    })
                
                section_summary_df = pd.DataFrame(section_summary)
                section_summary_df.to_excel(writer, sheet_name='Section_Summary', index=False)
        
        return filepath
    
    def _export_psd_data(self, psd_data: Dict, output_dir: str, pile_name: str, timestamp: str) -> str:
        """导出PSD分析数据"""
        filename = f"{pile_name}_psd_analysis_{timestamp}.xlsx"
        filepath = os.path.join(output_dir, filename)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # PSD深度剖面
            if 'depth_profile' in psd_data:
                psd_profile_df = pd.DataFrame(psd_data['depth_profile'])
                psd_profile_df = psd_profile_df.sort_values(['section_no', 'depth'])
                psd_profile_df.to_excel(writer, sheet_name='PSD_Depth_Profile', index=False)
            
            # 按截面的PSD统计
            if 'by_section' in psd_data:
                section_psd_stats = []
                for section_key, section_stats in psd_data['by_section'].items():
                    section_psd_stats.append({
                        'Section': section_key,
                        'Point_Count': section_stats.get('count', 0),
                        'PSD_Min': section_stats.get('min', ''),
                        'PSD_Max': section_stats.get('max', ''),
                        'PSD_Mean': section_stats.get('mean', ''),
                        'PSD_Std': section_stats.get('std', '')
                    })
                
                section_psd_df = pd.DataFrame(section_psd_stats)
                section_psd_df.to_excel(writer, sheet_name='PSD_by_Section', index=False)
            
            # 整体PSD统计
            if 'overall_statistics' in psd_data:
                overall_stats = psd_data['overall_statistics']
                overall_psd_df = pd.DataFrame([{
                    'Total_Points': overall_stats.get('total_points', 0),
                    'PSD_Min': overall_stats.get('min', ''),
                    'PSD_Max': overall_stats.get('max', ''),
                    'PSD_Mean': overall_stats.get('mean', ''),
                    'PSD_Median': overall_stats.get('median', ''),
                    'PSD_Std': overall_stats.get('std', ''),
                    'PSD_Range': overall_stats.get('range', '')
                }])
                overall_psd_df.to_excel(writer, sheet_name='Overall_PSD_Stats', index=False)
        
        return filepath
    
    def _export_complete_analysis(self, analysis_result: Dict, output_dir: str, pile_name: str, timestamp: str) -> str:
        """导出完整分析报告"""
        filename = f"{pile_name}_complete_analysis_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        # 添加导出信息
        export_info = {
            'export_timestamp': datetime.now().isoformat(),
            'pile_name': pile_name,
            'exporter_version': '1.0',
            'data_description': {
                'section_combinations': '截面组合数据 (1-2, 1-3, 2-3等)',
                'depth_analysis': '每个深度的波幅、波速、PSD数据',
                'critical_values': '临界波速、临界波幅、临界PSD数据',
                'acoustic_time_analysis': '声时versus波幅关系数据',
                'psd_analysis': 'PSD功率谱密度分析数据'
            }
        }
        
        complete_data = {
            'export_info': export_info,
            'analysis_result': analysis_result
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(complete_data, f, indent=2, ensure_ascii=False, default=str)
        
        return filepath
    
    def _get_parameter_unit(self, param_type: str) -> str:
        """获取参数单位"""
        unit_map = {
            'velocity_critical': 'm/s',
            'amplitude_critical': 'V',
            'energy_critical': 'J',
            'psd_critical': 'V²·s/m'
        }
        return unit_map.get(param_type, '')
    
    def export_summary_report(self, pile_data: Dict[str, Any], output_dir: str = "section_exports") -> str:
        """导出汇总报告"""
        try:
            analysis_result = self.analyzer.analyze_pile_sections(pile_data)
            
            pile_name = pile_data.get('Pile', 'Unknown_Pile')
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{pile_name}_summary_report_{timestamp}.txt"
            filepath = os.path.join(output_dir, filename)
            
            os.makedirs(output_dir, exist_ok=True)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write(f"桩基截面分析汇总报告\n")
                f.write(f"Pile Section Analysis Summary Report\n")
                f.write("=" * 80 + "\n\n")
                
                # 基本信息
                pile_info = analysis_result['pile_info']
                f.write(f"桩基信息 (Pile Information):\n")
                f.write(f"  桩名 (Pile Name): {pile_info['pile_name']}\n")
                f.write(f"  桩长 (Length): {pile_info['pile_length']} m\n")
                f.write(f"  桩径 (Diameter): {pile_info['diameter']} m\n")
                f.write(f"  检测日期 (Detection Date): {pile_info['detecting_date']}\n")
                f.write(f"  截面数量 (Total Sections): {pile_info['total_sections']}\n\n")
                
                # 截面组合
                f.write(f"截面组合 (Section Combinations):\n")
                combinations = analysis_result['section_combinations']
                for combo_name in combinations.keys():
                    f.write(f"  - {combo_name}\n")
                f.write(f"  总计: {len(combinations)} 个组合\n\n")
                
                # 临界值
                f.write(f"临界值 (Critical Values):\n")
                critical_values = analysis_result['critical_values']
                for param, values in critical_values.items():
                    if isinstance(values, dict) and 'mean' in values:
                        f.write(f"  {param}:\n")
                        f.write(f"    平均值: {values.get('mean', 0):.2f}\n")
                        f.write(f"    临界下限: {values.get('critical_low', 0):.2f}\n")
                        f.write(f"    临界上限: {values.get('critical_high', 0):.2f}\n")
                f.write("\n")
                
                # 声时vs波幅相关性
                acoustic_analysis = analysis_result['acoustic_time_analysis']
                if 'overall_relationship' in acoustic_analysis:
                    overall = acoustic_analysis['overall_relationship']
                    f.write(f"声时vs波幅相关性 (Acoustic Time vs Amplitude Correlation):\n")
                    f.write(f"  相关系数: {overall.get('correlation_coefficient', 0):.3f}\n")
                    f.write(f"  数据点数: {overall.get('total_points', 0)}\n")
                    
                    if 'correlation_analysis' in acoustic_analysis:
                        corr_analysis = acoustic_analysis['correlation_analysis']
                        f.write(f"  相关性强度: {corr_analysis.get('strength', 'Unknown')}\n")
                        f.write(f"  显著性: {corr_analysis.get('significance', 'Unknown')}\n")
                f.write("\n")
                
                # 数据完整性
                summary_stats = analysis_result['summary_statistics']
                if 'data_completeness' in summary_stats:
                    completeness = summary_stats['data_completeness']
                    f.write(f"数据完整性 (Data Completeness):\n")
                    f.write(f"  总测点数: {completeness.get('total_points', 0)}\n")
                    f.write(f"  波速完整性: {completeness.get('velocity_completeness', 0):.1f}%\n")
                    f.write(f"  波幅完整性: {completeness.get('amplitude_completeness', 0):.1f}%\n")
                    f.write(f"  能量完整性: {completeness.get('energy_completeness', 0):.1f}%\n")
                    f.write(f"  整体完整性: {completeness.get('overall_completeness', 0):.1f}%\n")
                
                f.write("\n" + "=" * 80 + "\n")
                f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"Error creating summary report: {str(e)}")
            raise
