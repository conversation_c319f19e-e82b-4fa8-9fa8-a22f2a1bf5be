#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Complete KBZ Export with Full Data Structure
测试完整的KBZ导出功能（包含完整数据结构）

This script tests the KBZ exporter with complete data structure
including all 159 measurement points from 0.10m to 15.90m for all sections.
"""

import sys
import os
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_processing.kbz_exporter import KBZExporter
from src.utils.logger import setup_logging


def generate_complete_measurement_data(base_velocity, base_amplitude, total_points=159):
    """生成完整的测量数据（159个测点，从0.10m到15.90m）"""
    import random
    measurements = []
    
    for i in range(total_points):
        depth = 0.10 + i * 0.10  # 从0.10m开始，间隔0.10m
        
        # 添加一些随机变化
        velocity_variation = random.uniform(-0.3, 0.3)
        amplitude_variation = random.uniform(-10, 10)
        
        # 在某些深度添加异常值模拟缺陷
        if depth in [4.0, 10.0]:  # 模拟缺陷位置
            velocity_variation -= 0.5
            amplitude_variation -= 15
        
        measurement = {
            "SEQ": i + 1,
            "H": depth,
            "T": 120.0 + random.uniform(-10, 10),
            "V": base_velocity + velocity_variation,
            "A": base_amplitude + amplitude_variation,
            "F": 0.00,
            "P": random.uniform(0.0, 2.0),
            "VNormFlag": 1 if abs(velocity_variation) > 0.4 else 0,
            "ANormFlag": 1 if abs(amplitude_variation) > 12 else 0,
            "FNormFlag": 0,
            "PNormFlag": 0,
            "FlawFlag": 1 if depth in [4.0, 10.0] else 0,
            "nValidFlag": 1
        }
        measurements.append(measurement)
    
    return measurements


def generate_complete_ultra_data(total_points=159):
    """生成完整的超声数据（159个测点）"""
    import random
    ultra_data = []
    
    for i in range(total_points):
        depth = 15.90 - i * 0.10  # 从桩底开始向上
        
        # 生成波形数据
        wave_data = []
        for j in range(1024):  # 1024个波形点
            if j < 50:
                # 前段平稳
                wave_data.append(2000 + random.randint(-50, 50))
            elif j < 200:
                # 波峰段
                peak_factor = 1 + 2 * (1 - abs(j - 125) / 75)
                wave_data.append(int(2000 * peak_factor) + random.randint(-100, 100))
            else:
                # 后段衰减
                decay_factor = max(0.1, 1 - (j - 200) / 800)
                wave_data.append(int(2000 * decay_factor) + random.randint(-50, 50))
        
        ultra_measurement = {
            "SEQ": i + 1,
            "T": 100.0 + random.uniform(-10, 10),
            "orgTime": 100.0 + random.uniform(-10, 10),
            "A": 130.0 + random.uniform(-15, 15),
            "F": 0.00,
            "fDist": depth,
            "validFlag": 1,
            "cName": f"{i+1:03d}-01",
            "fOrgA": 130.0 + random.uniform(-15, 15),
            "PSD": random.uniform(0.0, 2.0),
            "WaveVelocity": 4.0 + random.uniform(-0.5, 0.5),
            "H": depth,
            "Amplification": random.randint(65, 75),
            "Baseline": 0,
            "Delay": random.randint(200, 400),
            "JudgeFlag": 0,
            "deleteFlag": 0,
            "Gain": random.randint(65, 75),
            "Start": 100,
            "FirstPeak": 150,
            "CursorX": 200,
            "CursorY": 300,
            "BaseCor": 0,
            "VNormFlag": 0,
            "ANormFlag": 0,
            "FNormFlag": 0,
            "PNormFlag": 0,
            "FlawFlag": 0,
            "Integrity": 1.0,
            "RealDist": depth,
            "WaveData": wave_data
        }
        ultra_data.append(ultra_measurement)
    
    return ultra_data


def create_complete_sample_data():
    """创建包含完整数据结构的示例数据"""
    # 基础参数
    sections_info = [
        {"name": "1-2", "distance": 470.0, "base_velocity": 4.127, "base_amplitude": 138.96},
        {"name": "1-3", "distance": 770.0, "base_velocity": 4.228, "base_amplitude": 131.98},
        {"name": "2-3", "distance": 800.0, "base_velocity": 4.500, "base_amplitude": 131.60}
    ]
    
    # 生成Result_Sections_Data
    result_sections_data = []
    for i, section_info in enumerate(sections_info):
        measurements = generate_complete_measurement_data(
            section_info["base_velocity"], 
            section_info["base_amplitude"]
        )
        
        # 计算统计值
        velocities = [m["V"] for m in measurements]
        amplitudes = [m["A"] for m in measurements]
        
        vm = sum(velocities) / len(velocities)
        am = sum(amplitudes) / len(amplitudes)
        sv = (sum((v - vm) ** 2 for v in velocities) / len(velocities)) ** 0.5
        sa = (sum((a - am) ** 2 for a in amplitudes) / len(amplitudes)) ** 0.5
        
        section_data = {
            "SectionNo": i,
            "SectionName": section_info["name"],
            "InitialDepth": 0.10,
            "Interval": 0.10,
            "PipeDist": section_info["distance"],
            "HeightCorrection": 0.00,
            "Direction": "正向",
            "nDirection": 1,
            "TotalPoints": 159,
            "TestedLength": 15.90,
            "CriticOfPsd": 200.00,
            "CriticalValue": "二倍",
            "Vc1": vm - 2 * sv,
            "Vc2": 0.000,
            "Ac1": am - 2 * sa,
            "Ac2": 0.000,
            "Fc1": 0.000,
            "Fc2": 0.000,
            "Coefficient1": 2.49,
            "Coefficient3": 0.00,
            "Vm": vm,
            "Sv": sv,
            "Am": am,
            "Sa": sa,
            "Fm": 0.00,
            "Sf": 0.000,
            "CveSpeed": sv / vm if vm > 0 else 0,
            "CveAmp": sa / am if am > 0 else 0,
            "fAveTime": vm,
            "data": measurements
        }
        result_sections_data.append(section_data)
    
    # 生成Test_Sections_Data
    test_sections_data = []
    for i, section_info in enumerate(sections_info):
        ultra_data = generate_complete_ultra_data()
        
        test_section = {
            "ObjectNo": i,
            "Object": section_info["name"],
            "TotalPoints": 159,
            "MaxSubNo": 1,
            "Dist": section_info["distance"],
            "SamplingLength": 1024,
            "SavedLength": 1024,
            "SamplingInterval": 0.40,
            "TrigVoltage": 250,
            "InitialTime": 13.80,
            "TriggerMode": 3,
            "Channel": 0,
            "UltraData": ultra_data
        }
        test_sections_data.append(test_section)
    
    return {
        "Pile": "KBZ1-9",
        "Sections": 3,
        "StrengthGrade": "C35",
        "CastingDate": "2024年10月12日",
        "DetectingDate": "2024年12月23日 15:54:26",
        "PileLength": 15.90,
        "Diameter": 1200.0,
        "TechnicalCode": "JGJ 106-2014",
        "Model": "U5700",
        "SerialNo": "U72007005N",
        "Certificate": "",
        "Tester": "",
        "QualificationNo": "",
        "Version": "ZBLU5700 V1.0.0",
        "TotalObjects": 3,
        "Project": "1223",
        "Address": "",
        "DesignUnit": "",
        "ConstructionUnit": "",
        "BuildingUnit": "",
        "SupervisionUnit": "",
        "MonitoringUnit": "",
        "EntrustingUnit": "",
        "DetectionUnit": "",
        "EntrustingDate": "2024年12月23日",
        "CalculationDate": "2024年12月23日",
        "Result_Sections_Data": result_sections_data,
        "Test_Sections_Data": test_sections_data
    }


def test_complete_kbz_export():
    """测试完整的KBZ导出功能"""
    print("Testing Complete KBZ Export with Full Data Structure...")
    print("=" * 60)
    
    try:
        # 设置日志
        setup_logging()
        
        # 创建完整示例数据
        pile_data = create_complete_sample_data()
        print(f"Created complete sample data for pile: {pile_data['Pile']}")
        print(f"Total sections: {len(pile_data['Result_Sections_Data'])}")
        
        for i, section in enumerate(pile_data['Result_Sections_Data']):
            print(f"  Section {i+1} ({section['SectionName']}): {len(section['data'])} measurement points")
        
        # 初始化导出器
        exporter = KBZExporter()
        print("✓ KBZ exporter initialized")
        
        # 创建输出目录
        output_dir = "./complete_test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 测试TXT导出
        print("\nTesting TXT export...")
        txt_file = exporter.export_to_txt(pile_data, output_dir, "KBZ1-9")
        print(f"✓ TXT file exported: {txt_file}")
        
        # 检查TXT文件内容
        with open(txt_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        print(f"  File size: {len(content)} characters")
        
        # 检查关键内容
        checks = [
            ("基础数据", "基础数据" in content),
            ("声波测试数据", "声波测试数据" in content),
            ("各测点声参量及波形", "各测点声参量及波形" in content),
            ("波形放大倍数", "波形放大倍数" in content),
            ("159个测点", content.count("159-01") >= 3),  # 应该有3个截面的159测点
            ("完整深度范围", "0.10" in content and "15.90" in content)
        ]
        
        for check_name, result in checks:
            status = "✓" if result else "✗"
            print(f"  {status} {check_name}")
        
        # 测试Excel导出
        print("\nTesting Excel export...")
        excel_file = exporter.export_to_excel(pile_data, output_dir, "KBZ1-9")
        print(f"✓ Excel file exported: {excel_file}")
        
        # 检查Excel文件
        import pandas as pd
        excel_data = pd.ExcelFile(excel_file)
        print(f"  Sheets: {excel_data.sheet_names}")
        
        # 检查数据表内容
        data_sheet = pd.read_excel(excel_file, sheet_name='数据表')
        print(f"  Data sheet shape: {data_sheet.shape}")
        print(f"  Expected 159 data rows + headers = ~167 total rows")
        
        print("\n" + "=" * 60)
        print("Complete KBZ Export Test Completed Successfully!")
        print(f"Output files:")
        print(f"  - {txt_file}")
        print(f"  - {excel_file}")
        print("\nData completeness:")
        print(f"  - Total measurement points per section: 159 (0.10m to 15.90m)")
        print(f"  - Total sections: 3")
        print(f"  - Total data points: {159 * 3} = 477")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_complete_kbz_export()
    sys.exit(0 if success else 1)
