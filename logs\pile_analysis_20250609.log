2025-06-09 15:06:32 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 15:06:32 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 15:06:32 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 15:06:32 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 15:06:32 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-09 15:06:32 [    INFO] src.utils.config_manager:56 - Config<PERSON>anager initialized with config directory: f:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:06:32 [    INFO] src.utils.config_manager:113 - Loaded configuration from: f:\2025\APiles_network\connection\pile_analysis_system\config\api_config.json
2025-06-09 15:06:32 [    INFO] src.utils.config_manager:56 - Config<PERSON><PERSON><PERSON> initialized with config directory: f:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:06:32 [    INFO] src.utils.config_manager:113 - Loaded configuration from: f:\2025\APiles_network\connection\pile_analysis_system\config\analysis_config.json
2025-06-09 15:06:32 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: f:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:06:32 [    INFO] src.utils.config_manager:113 - Loaded configuration from: f:\2025\APiles_network\connection\pile_analysis_system\config\analysis_config.json
2025-06-09 15:06:32 [ WARNING] src.analysis_integration.gz_integration:87 - gz_traditional_analysis.py not found, trying fallback import
2025-06-09 15:06:32 [ WARNING] src.analysis_integration.gz_integration:173 - Failed to import gz_traditional_analysis: gz_traditional_analysis.py not found in any expected location
2025-06-09 15:06:32 [ WARNING] src.analysis_integration.gz_integration:175 - Creating mock analyzer for testing
2025-06-09 15:06:32 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-09 15:06:35 [    INFO] src.gui.data_loader_window:2823 - Status: 正在初始化组件...
2025-06-09 15:06:35 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: f:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:06:35 [    INFO] src.utils.config_manager:113 - Loaded configuration from: f:\2025\APiles_network\connection\pile_analysis_system\config\api_config.json
2025-06-09 15:06:35 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: f:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:06:35 [    INFO] src.utils.config_manager:113 - Loaded configuration from: f:\2025\APiles_network\connection\pile_analysis_system\config\analysis_config.json
2025-06-09 15:06:35 [    INFO] src.gui.data_loader_window:2823 - Status: 组件初始化完成
2025-06-09 15:06:35 [    INFO] src.gui.data_loader_window:1674 - Data loader components initialized successfully
2025-06-09 15:06:35 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-09 15:06:36 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-09 15:06:36 [    INFO] src.gui.data_loader_window:2823 - Status: 正在获取API数据...
2025-06-09 15:06:37 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-09 15:06:37 [    INFO] src.gui.data_loader_window:2823 - Status: API数据获取完成
2025-06-09 15:06:37 [    INFO] src.gui.data_loader_window:1887 - Data preview updated successfully
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:45 - 开始分析桩基数据: 测试桩基_file
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:46 - 桩基数据键: ['Pile', 'PileLength', 'Diameter', 'DetectingDate', 'Sections', 'Result_Sections_Data']
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:50 - 截面数据数量: 2
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:55 - 截面 1: 3 个测点
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 1, 'Depth': 2.0, 'Velocity': 95.0, 'Amplitude': 2.0, 'Energy': 190.0, 'Quality': 'Good'}
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:55 - 截面 2: 3 个测点
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:59 - 第一个测点字段: ['PointNo', 'Depth', 'Velocity', 'Amplitude', 'Energy', 'Quality']
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:60 - 第一个测点数据: {'PointNo': 4, 'Depth': 12.0, 'Velocity': 88.0, 'Amplitude': 2.8, 'Energy': 246.4, 'Quality': 'Good'}
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:74 - 深度分析结果数量: 6
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_2.0m: 1 个测量值
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 1, 'velocity': 95.0, 'amplitude': 2.0, 'energy': 190.0, 'psd': 0.042105263157894736, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.021052631578947368}
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_5.0m: 1 个测量值
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 2, 'velocity': 92.0, 'amplitude': 2.2, 'energy': 202.4, 'psd': 0.05260869565217392, 'quality': 'Good', 'temperature': None, 'acoustic_time': 0.05434782608695652}
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:78 - 深度 depth_8.0m: 1 个测量值
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:81 - 第一个测量值: {'section_no': 1, 'point_no': 3, 'velocity': 89.0, 'amplitude': 2.5, 'energy': 222.5, 'psd': 0.0702247191011236, 'quality': 'Fair', 'temperature': None, 'acoustic_time': 0.0898876404494382}
2025-06-09 15:07:01 [    INFO] src.data_processing.section_analysis:83 - Section analysis completed for pile: 测试桩基_file
2025-06-09 15:07:09 [    INFO] src.gui.data_loader_window:2823 - Status: 请确保已选择单个桩基进行分析
2025-06-09 15:07:10 [    INFO] src.gui.data_loader_window:2823 - Status: 请确保已选择单个桩基进行分析
2025-06-09 15:07:11 [    INFO] src.gui.data_loader_window:2823 - Status: 请确保已选择单个桩基进行调试
2025-06-09 15:07:11 [    INFO] src.gui.data_loader_window:2823 - Status: 请确保已选择单个桩基进行分析
2025-06-09 15:24:11 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 15:24:11 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 15:24:11 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 15:24:11 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 15:24:11 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: F:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:24:11 [    INFO] src.utils.config_manager:113 - Loaded configuration from: F:\2025\APiles_network\connection\pile_analysis_system\config\api_config.json
2025-06-09 15:24:11 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-09 15:24:20 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-09 15:24:20 [    INFO] src.data_acquisition.api_client:280 - API client closed
2025-06-09 15:24:20 [    INFO] src.data_processing.kbz_exporter:51 - TXT file exported: ./test_output\KBZ1-9.TXT
2025-06-09 15:24:21 [    INFO] src.data_processing.kbz_exporter:90 - Excel file exported: ./test_output\KBZ1-9.xlsx
2025-06-09 15:25:00 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 15:25:00 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 15:25:00 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 15:25:00 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 15:25:00 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: F:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:25:00 [    INFO] src.utils.config_manager:113 - Loaded configuration from: F:\2025\APiles_network\connection\pile_analysis_system\config\api_config.json
2025-06-09 15:25:00 [    INFO] __main__:144 - 系统初始化完成
2025-06-09 15:28:38 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 15:28:38 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 15:28:38 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 15:28:38 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 15:28:39 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: F:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:28:39 [    INFO] src.utils.config_manager:113 - Loaded configuration from: F:\2025\APiles_network\connection\pile_analysis_system\config\api_config.json
2025-06-09 15:28:39 [    INFO] __main__:144 - 系统初始化完成
2025-06-09 15:28:53 [    INFO] __main__:144 - 开始获取桩基数据: 512856130
2025-06-09 15:28:53 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-09 15:29:18 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-09 15:29:18 [    INFO] __main__:144 - API数据获取成功
2025-06-09 15:29:18 [    INFO] src.data_processing.kbz_exporter:51 - TXT file exported: F:/2025/APiles_network/connection/pile_analysis_system/New folder\KBZ1-9.TXT
2025-06-09 15:29:18 [    INFO] __main__:144 - TXT文件导出成功: F:/2025/APiles_network/connection/pile_analysis_system/New folder\KBZ1-9.TXT
2025-06-09 15:29:18 [    INFO] src.data_processing.kbz_exporter:90 - Excel file exported: F:/2025/APiles_network/connection/pile_analysis_system/New folder\KBZ1-9.xlsx
2025-06-09 15:29:18 [    INFO] __main__:144 - Excel文件导出成功: F:/2025/APiles_network/connection/pile_analysis_system/New folder\KBZ1-9.xlsx
2025-06-09 15:29:18 [    INFO] __main__:144 - 数据导出完成，共导出 2 个文件
2025-06-09 15:36:09 [    INFO] src.data_acquisition.api_client:280 - API client closed
2025-06-09 15:41:40 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 15:41:40 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 15:41:40 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 15:41:40 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 15:41:40 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: F:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:41:40 [    INFO] src.utils.config_manager:113 - Loaded configuration from: F:\2025\APiles_network\connection\pile_analysis_system\config\api_config.json
2025-06-09 15:41:40 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-09 15:41:49 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-09 15:41:49 [    INFO] src.data_acquisition.api_client:280 - API client closed
2025-06-09 15:52:47 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 15:52:47 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 15:52:47 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 15:52:47 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 15:52:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: F:\2025\APiles_network\connection\pile_analysis_system\config
2025-06-09 15:52:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: F:\2025\APiles_network\connection\pile_analysis_system\config\api_config.json
2025-06-09 15:52:47 [    INFO] __main__:144 - 系统初始化完成
2025-06-09 15:52:54 [    INFO] __main__:144 - 开始获取桩基数据: 512856130
2025-06-09 15:52:54 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-09 15:52:59 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://120.79.201.77/pilediag/pile/pileJson
2025-06-09 15:52:59 [    INFO] __main__:144 - 导出失败: API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://120.79.201.77/pilediag/pile/pileJson
2025-06-09 15:53:35 [    INFO] __main__:144 - 开始获取桩基数据: 512856130
2025-06-09 15:53:35 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-09 15:53:37 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-09 15:53:37 [    INFO] __main__:144 - API数据获取成功
2025-06-09 15:53:37 [   ERROR] src.data_processing.kbz_exporter:55 - Failed to export TXT file: Unknown format code 'd' for object of type 'str'
2025-06-09 15:53:37 [    INFO] __main__:144 - 导出失败: Unknown format code 'd' for object of type 'str'
2025-06-09 15:53:56 [    INFO] src.data_acquisition.api_client:280 - API client closed
2025-06-09 15:54:01 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 15:54:01 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 15:54:01 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 15:54:01 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 15:54:01 [    INFO] src.data_processing.kbz_exporter:51 - TXT file exported: ./enhanced_test_output\KBZ1-9.TXT
2025-06-09 15:54:01 [    INFO] src.data_processing.kbz_exporter:90 - Excel file exported: ./enhanced_test_output\KBZ1-9.xlsx
2025-06-09 16:12:36 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-09 16:12:36 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-09 16:12:36 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250609.log
2025-06-09 16:12:36 [    INFO] src.utils.logger:103 - Console output: True
2025-06-09 16:12:36 [    INFO] src.data_processing.kbz_exporter:51 - TXT file exported: ./final_complete_output\KBZ1-9.TXT
2025-06-09 16:12:36 [    INFO] src.data_processing.kbz_exporter:90 - Excel file exported: ./final_complete_output\KBZ1-9.xlsx
