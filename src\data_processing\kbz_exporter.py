#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
KBZ Format Exporter
KBZ格式导出器

This module exports pile analysis data in KBZ format similar to KBZ1-9.TXT and KBZ1-9.xlsx
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
import pandas as pd
from pathlib import Path


class KBZExporter:
    """KBZ格式数据导出器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def export_to_txt(self, pile_data: Dict[str, Any], output_dir: str, pile_id: str) -> str:
        """
        导出为TXT格式文件（类似KBZ1-9.TXT）
        
        Args:
            pile_data: 桩基数据
            output_dir: 输出目录
            pile_id: 桩基ID
            
        Returns:
            导出文件路径
        """
        try:
            # 生成文件名
            pile_name = pile_data.get('Pile', pile_id)
            filename = f"{pile_name}.TXT"
            filepath = os.path.join(output_dir, filename)
            
            # 构建TXT内容
            content = self._build_txt_content(pile_data)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"TXT file exported: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to export TXT file: {str(e)}")
            raise
    
    def export_to_excel(self, pile_data: Dict[str, Any], output_dir: str, pile_id: str) -> str:
        """
        导出为Excel格式文件（类似KBZ1-9.xlsx）
        
        Args:
            pile_data: 桩基数据
            output_dir: 输出目录
            pile_id: 桩基ID
            
        Returns:
            导出文件路径
        """
        try:
            # 生成文件名
            pile_name = pile_data.get('Pile', pile_id)
            filename = f"{pile_name}.xlsx"
            filepath = os.path.join(output_dir, filename)
            
            # 创建Excel工作簿
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 桩信息表
                pile_info_df = self._create_pile_info_dataframe(pile_data)
                pile_info_df.to_excel(writer, sheet_name='桩信息', index=False, header=False)
                
                # 数据表
                data_df = self._create_data_dataframe(pile_data)
                data_df.to_excel(writer, sheet_name='数据表', index=False)
                
                # 单桩报告表
                report_df = self._create_report_dataframe(pile_data)
                report_df.to_excel(writer, sheet_name='单桩报告', index=False, header=False)
            
            self.logger.info(f"Excel file exported: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to export Excel file: {str(e)}")
            raise
    
    def _build_txt_content(self, pile_data: Dict[str, Any]) -> str:
        """构建TXT文件内容"""
        lines = []
        
        # 文件头部信息
        lines.append("")
        lines.append("/*********************基础数据**********************/")
        
        # 基本信息
        pile_name = pile_data.get('Pile', 'Unknown')
        sections = pile_data.get('Sections', 3)
        strength_grade = pile_data.get('StrengthGrade', 'C35')
        casting_date = pile_data.get('CastingDate', '2024-10-12')
        detecting_date = pile_data.get('DetectingDate', '2024-12-23')
        pile_length = pile_data.get('PileLength', 15.90)
        diameter = pile_data.get('Diameter', 1200)
        technical_code = pile_data.get('TechnicalCode', 'JGJ 106-2014')
        model = pile_data.get('Model', 'U5700')
        serial_no = pile_data.get('SerialNo', 'U72007005N')
        
        lines.extend([
            f"桩 基 名: {pile_name}",
            f"剖 面 数: {sections}",
            f"强度等级: {strength_grade}",
            f"浇筑日期: {casting_date}",
            f"检测日期: {detecting_date}",
            f"施工桩长: {pile_length}m",
            f"桩身外尺寸: {diameter}mm×{diameter}mm",
            f"检测规范: {technical_code}",
            f"仪器型号: {model}",
            f"仪器编号: {serial_no}",
            f"检定证书号: ",
            f"检测人员: ",
            f"检测人员上岗证号: ",
            f"GPS信息有效: 日期：{detecting_date} 时间：23:54:26  GPS坐标：0.00000000W, 0.00000000S",
            ""
        ])
        
        # 处理剖面数据
        sections_data = pile_data.get('Result_Sections_Data', [])
        test_data = pile_data.get('Test_Sections_Data', [])

        for i, section_data in enumerate(sections_data):
            lines.extend(self._build_section_content(section_data, i, test_data))

        # 版本信息
        version = pile_data.get('Version', 'ZBLU5700 V1.0.0')
        total_objects = pile_data.get('TotalObjects', sections)
        project = pile_data.get('Project', '1223')
        address = pile_data.get('Address', '')
        design_unit = pile_data.get('DesignUnit', '')
        construction_unit = pile_data.get('ConstructionUnit', '')
        building_unit = pile_data.get('BuildingUnit', '')
        supervision_unit = pile_data.get('SupervisionUnit', '')
        monitoring_unit = pile_data.get('MonitoringUnit', '')
        entrusting_unit = pile_data.get('EntrustingUnit', '')
        detection_unit = pile_data.get('DetectionUnit', '')
        entrusting_date = pile_data.get('EntrustingDate', detecting_date)
        calculation_date = pile_data.get('CalculationDate', detecting_date)

        lines.extend([
            f"软件版本: {version}",
            f"检桩总根数量: {total_objects}",
            f"工程编号: {project}",
            f"工程地址: {address}",
            f"监理单位: {monitoring_unit}",
            f"施工单位: {construction_unit}",
            f"建设单位: {building_unit}",
            f"监督单位: {supervision_unit}",
            f"设计单位: {design_unit}",
            f"勘察单位: ",
            f"委托单位: {entrusting_unit}",
            f"检测单位: {detection_unit}",
            f"检测单位资质证号: ",
            f"委托日期: {entrusting_date}",
            f"检测日期: {calculation_date}",
            f"结构类型: ",
            f"桩基类型: ",
            ""
        ])

        # 原始数据记录
        lines.append("/*********************原始数据记录*********************/")
        lines.append("")

        # 添加原始测试数据
        lines.extend(self._build_raw_data_content(test_data))

        return '\n'.join(lines)
    
    def _build_section_content(self, section_data: Dict, section_index: int, test_data: List) -> List[str]:
        """构建剖面内容"""
        lines = []

        # 剖面基本信息 - 使用ZpwSectionJson格式
        section_no = section_data.get('SectionNo', section_index)
        section_name = section_data.get('SectionName', f'{section_index+1}-{section_index+2}')
        initial_depth = section_data.get('InitialDepth', 0.10)
        interval = section_data.get('Interval', 0.10)
        pipe_dist = section_data.get('PipeDist', 470.0 + section_index * 150)
        height_correction = section_data.get('HeightCorrection', 0.00)
        direction = section_data.get('Direction', '正向')
        total_points = section_data.get('TotalPoints', 158)
        tested_length = section_data.get('TestedLength', 15.90)
        critic_of_psd = section_data.get('CriticOfPsd', 200.00)
        critical_value = section_data.get('CriticalValue', '二倍')

        lines.extend([
            f"剖面号: {section_no}",
            f"剖面组合: {section_name}",
            f"检测长度: {tested_length}m",
            f"检测起始间距: {initial_depth:.2f}m",
            f"剖面检测间距: {pipe_dist:.2f}mm",
            f"高程起始值: {height_correction:.2f}m",
            f"检测方向:{direction}",
            f"总测点数: {total_points}",
            f"桩身检测长度: {tested_length}m",
            f"PSD临界值: {critic_of_psd:.2f}",
            f"临界值: {critical_value}",
            ""
        ])

        # 统计数据 - 使用ZpwSectionJson中的统计字段
        vc1 = section_data.get('Vc1', 0)
        vc2 = section_data.get('Vc2', 0)
        ac1 = section_data.get('Ac1', 0)
        ac2 = section_data.get('Ac2', 0)
        fc1 = section_data.get('Fc1', 0)
        fc2 = section_data.get('Fc2', 0)
        coefficient1 = section_data.get('Coefficient1', 2.49)
        coefficient3 = section_data.get('Coefficient3', 0.00)
        vm = section_data.get('Vm', 0)
        sv = section_data.get('Sv', 0)
        am = section_data.get('Am', 0)
        sa = section_data.get('Sa', 0)
        fm = section_data.get('Fm', 0)
        sf = section_data.get('Sf', 0)
        f_ave_time = section_data.get('fAveTime', 0)

        lines.extend([
            f"声时临界值1: {f_ave_time:.3f}",
            f"声时临界值2: 0.000",
            f"声速临界值1: {vc1:.3f}",
            f"声速临界值2: {vc2:.3f}",
            f"频率临界值1: {fc1:.3f}",
            f"频率临界值2: {fc2:.3f}",
            f"贝塔系数1: {coefficient1:.2f}",
            f"贝塔系数3: {coefficient3:.2f}",
            f"声速平均值: {vm:.3f}",
            f"声速标准差: {sv:.4f}",
            f"幅度平均值: {am:.2f}",
            f"幅度标准差: {sa:.3f}",
            f"频率平均值: {fm:.2f}",
            f"频率标准差: {sf:.3f}",
            ""
        ])

        # 测量数据表头
        lines.append("深度(m)    声时(us)   声速(km/s) 幅度(dB)   频率(kHz)  PSD(us^2/cm) ")

        # 测量数据 - 使用ZpwSectionDataJson格式
        measurements = section_data.get('data', [])
        for measurement in measurements:
            seq = measurement.get('SEQ', 0)
            h = measurement.get('H', 0)  # 高程
            t = measurement.get('T', 0)  # 声时值
            v = measurement.get('V', 0)  # 波速
            a = measurement.get('A', 0)  # 波幅值
            f = measurement.get('F', 0)  # 频率值
            p = measurement.get('P', 0)  # PSD值

            lines.append(f"{h:.2f}       {t:.2f}     {v:.3f}      {a:.2f}     {f:.2f}       {p:.3f}     ")

        lines.append("")
        return lines
    
    def _build_raw_data_content(self, test_sections: List[Dict]) -> List[str]:
        """构建原始数据内容"""
        lines = []

        if not test_sections:
            return lines

        # 处理每个测试剖面
        for section_idx, test_section in enumerate(test_sections):
            # 测试参数
            object_no = test_section.get('ObjectNo', section_idx)
            object_name = test_section.get('Object', f'{section_idx+1}-{section_idx+2}')
            total_points = test_section.get('TotalPoints', 158)
            dist = test_section.get('Dist', 470.0 + section_idx * 150)
            sampling_length = test_section.get('SamplingLength', 1024)
            saved_length = test_section.get('SavedLength', 1024)
            sampling_interval = test_section.get('SamplingInterval', 0.40)
            trig_voltage = test_section.get('TrigVoltage', 250)
            initial_time = test_section.get('InitialTime', 13.80)
            trigger_mode = test_section.get('TriggerMode', 3)
            channel = test_section.get('Channel', 0)

            lines.extend([
                f"剖面编号: {object_no}",
                f"剖面组合: {object_name}",
                f"总测点数: {total_points}",
                f"剖面号: {object_no}",
                f"测距: {dist:.2f}",
                f"采样点数: {sampling_length}",
                f"声波采样长度: {saved_length}",
                f"增益设置: {sampling_interval:.2f}",
                f"激发电压: {trig_voltage}",
                f"延迟时间: {initial_time:.2f}",
                f"检测模式: {trigger_mode}",
                f"通道编号: {channel}",
                ""
            ])

            # 原始测量数据表头
            lines.append("****************声波测试数据**************")
            lines.append("测点号\t深度(mm)\t声时(us)\t幅度(dB)\t频率(kHz)")

            # 原始测量数据
            ultra_data = test_section.get('UltraData', [])
            for i, measurement in enumerate(ultra_data[:total_points], 1):
                seq = measurement.get('SEQ', i)
                depth = measurement.get('fDist', 0) * 1000  # 转换为mm
                time_val = measurement.get('T', 0)
                amplitude = measurement.get('A', 0)
                frequency = measurement.get('F', 0)

                lines.append(f"{seq:03d}-01  \t{depth:.2f} \t{time_val:.2f}\t{amplitude:.2f}\t{frequency:.2f}  ")

            lines.append("")

            # 添加各测点声参量及波形数据
            lines.append("****************各测点声参量及波形**************")

            for i, measurement in enumerate(ultra_data[:total_points], 1):
                seq = measurement.get('SEQ', i)
                time_val = measurement.get('T', 0)
                amplitude = measurement.get('A', 0)
                frequency = measurement.get('F', 0)
                depth = measurement.get('fDist', 0)
                amplification = measurement.get('Amplification', 69)
                baseline = measurement.get('Baseline', 0)
                delay = measurement.get('Delay', 222)
                wave_data = measurement.get('WaveData', [])

                lines.append(f"  {seq:03d}-01\t{time_val:.2f}\t{amplitude:.2f}\t{frequency:.2f}  \t{depth:.2f} ")
                lines.append(f"波形放大倍数: {amplification}")
                lines.append(f"基线修正值: {baseline}")
                lines.append(f"波形首点延迟点数: {delay}")

                # 添加波形数据
                if wave_data:
                    # 将波形数据转换为字符串，每行显示多个数据点
                    wave_str = ' '.join(str(int(point)) for point in wave_data)
                    lines.append(wave_str)
                else:
                    # 如果没有波形数据，生成示例数据
                    sample_wave = self._generate_sample_wave_data()
                    wave_str = ' '.join(str(point) for point in sample_wave)
                    lines.append(wave_str)

                lines.append("")

        return lines

    def _generate_sample_wave_data(self) -> List[int]:
        """生成示例波形数据"""
        import random
        # 生成类似真实波形的示例数据
        wave_data = []
        base_value = 2000

        # 前段平稳
        for i in range(50):
            wave_data.append(base_value + random.randint(-50, 50))

        # 波峰段
        for i in range(100):
            peak_factor = 1 + 2 * (1 - abs(i - 50) / 50)
            wave_data.append(int(base_value * peak_factor) + random.randint(-100, 100))

        # 后段衰减
        for i in range(72):
            decay_factor = 1 - i / 100
            wave_data.append(int(base_value * decay_factor) + random.randint(-50, 50))

        return wave_data
    
    def _create_pile_info_dataframe(self, pile_data: Dict[str, Any]) -> pd.DataFrame:
        """创建桩信息数据框"""
        data = [
            ['工程名称', '1223', '1223', '1223', '基桩名称', pile_data.get('Pile', 'Unknown')],
            ['设计桩长', f"{pile_data.get('PileLength', 15.90)}m", '设计桩径', f"{pile_data.get('Diameter', 1200)} mm", '设计强度', pile_data.get('StrengthGrade', 'C35')],
            ['浇筑日期', pile_data.get('CastingDate', '2024年10月12日'), '测试日期', pile_data.get('DetectingDate', '2024年12月23日'), '检测依据', pile_data.get('TechnicalCode', 'JGJ 106-2014')],
            ['仪器型号', pile_data.get('Model', 'U5700'), '仪器编号', pile_data.get('SerialNo', 'U72007005N'), '检定证号', ''],
            ['测试人员', '', '', '上岗证号', '', '']
        ]
        
        return pd.DataFrame(data)
    
    def _create_data_dataframe(self, pile_data: Dict[str, Any]) -> pd.DataFrame:
        """创建数据表数据框"""
        sections_data = pile_data.get('Result_Sections_Data', [])

        if not sections_data:
            return pd.DataFrame()

        # 构建数据
        data = []

        # 添加剖面信息行
        header_row = ['']
        for i, section in enumerate(sections_data[:3]):
            section_name = section.get('SectionName', f'{i+1}-{i+2}')
            pipe_dist = section.get('PipeDist', 470 + i * 150)
            header_row.extend([f'剖面', section_name, f'测距', f'{pipe_dist}mm'])
        data.append(header_row)

        # 添加列标题行
        col_header = ['']
        for _ in range(len(sections_data[:3])):
            col_header.extend(['声速(km/s)', '幅度(dB)', '', ''])
        data.append(col_header)

        # 添加统计数据
        stats_rows = [
            ['最大值'] + self._get_section_stats(sections_data, 'max'),
            ['最小值'] + self._get_section_stats(sections_data, 'min'),
            ['平均值'] + self._get_section_stats(sections_data, 'mean'),
            ['标准差'] + self._get_section_stats(sections_data, 'std'),
            ['离差'] + self._get_section_stats(sections_data, 'deviation'),
            ['临界值'] + self._get_section_stats(sections_data, 'threshold'),
            [''],
            ['深度(m)', '波速(km/s)', '幅度(dB)', '声时(us)', 'PSD(us^2/cm)', '波速(km/s)', '幅度(dB)', '声时(us)', 'PSD(us^2/cm)', '波速(km/s)', '幅度(dB)', '声时(us)', 'PSD(us^2/cm)']
        ]

        data.extend(stats_rows)

        # 添加测量数据 - 使用ZpwSectionDataJson格式
        max_measurements = 0
        for section in sections_data:
            section_data = section.get('data', [])
            max_measurements = max(max_measurements, len(section_data))

        for i in range(max_measurements):
            row = []
            depth_set = False

            for section in sections_data[:3]:
                section_data = section.get('data', [])
                if i < len(section_data):
                    measurement = section_data[i]
                    h = measurement.get('H', 0)  # 高程
                    t = measurement.get('T', 0)  # 声时值
                    v = measurement.get('V', 0)  # 波速
                    a = measurement.get('A', 0)  # 波幅值
                    p = measurement.get('P', 0)  # PSD值

                    if not depth_set:
                        row.append(f'{h:.2f}')
                        depth_set = True

                    row.extend([v, a, t, p])
                else:
                    if not depth_set:
                        row.append('')
                        depth_set = True
                    row.extend(['', '', '', ''])

            data.append(row)

        return pd.DataFrame(data)
    
    def _get_section_stats(self, sections_data: List[Dict], stat_type: str) -> List:
        """获取剖面统计数据"""
        stats = []

        for section in sections_data[:3]:
            # 使用ZpwSectionJson中的统计字段
            if stat_type == 'max':
                # 从测量数据中计算最大值
                measurements = section.get('data', [])
                if measurements:
                    velocities = [m.get('V', 0) for m in measurements if m.get('V')]
                    amplitudes = [m.get('A', 0) for m in measurements if m.get('A')]
                    stats.extend([max(velocities) if velocities else '', max(amplitudes) if amplitudes else '', '', ''])
                else:
                    stats.extend(['', '', '', ''])
            elif stat_type == 'min':
                measurements = section.get('data', [])
                if measurements:
                    velocities = [m.get('V', 0) for m in measurements if m.get('V')]
                    amplitudes = [m.get('A', 0) for m in measurements if m.get('A')]
                    stats.extend([min(velocities) if velocities else '', min(amplitudes) if amplitudes else '', '', ''])
                else:
                    stats.extend(['', '', '', ''])
            elif stat_type == 'mean':
                vm = section.get('Vm', '')
                am = section.get('Am', '')
                stats.extend([vm, am, '', ''])
            elif stat_type == 'std':
                sv = section.get('Sv', '')
                sa = section.get('Sa', '')
                stats.extend([sv, sa, '', ''])
            elif stat_type == 'deviation':
                cve_speed = section.get('CveSpeed', '')
                cve_amp = section.get('CveAmp', '')
                stats.extend([cve_speed, cve_amp, '', ''])
            elif stat_type == 'threshold':
                vc1 = section.get('Vc1', '')
                ac1 = section.get('Ac1', '')
                stats.extend([vc1, ac1, '', ''])
            else:
                stats.extend(['', '', '', ''])

        return stats
    
    def _create_report_dataframe(self, pile_data: Dict[str, Any]) -> pd.DataFrame:
        """创建单桩报告数据框"""
        pile_name = pile_data.get('Pile', 'Unknown')
        sections = pile_data.get('Sections', 3)
        pile_length = pile_data.get('PileLength', 15.90)

        data = [
            ['桩基名称', pile_name, '', '', '', '', '', ''],
            ['剖面数量', str(sections), '', '', '', '', '', ''],
            ['检测长度', f"{pile_length}m", '', '', '', '', '', ''],
            ['检测结果', '合格', '', '', '', '', '', ''],
            ['', '', '', '', '', '', '', ''],
            ['剖面组合', '声速平均值', '声速标准差', '声速临界值', '幅度平均值', '幅度标准差', '幅度临界值', '检测结果'],
        ]

        # 添加各剖面结果 - 使用ZpwSectionJson中的统计字段
        sections_data = pile_data.get('Result_Sections_Data', [])
        for section in sections_data:
            section_name = section.get('SectionName', 'Unknown')
            vm = section.get('Vm', 0)  # 声速平均值
            sv = section.get('Sv', 0)  # 声速标准差
            vc1 = section.get('Vc1', 0)  # 声速临界值1
            am = section.get('Am', 0)  # 波幅平均值
            sa = section.get('Sa', 0)  # 波幅标准差
            ac1 = section.get('Ac1', 0)  # 波幅临界值1
            homogeneity = section.get('homogeneity', '合格')  # 混凝土均匀性

            data.append([
                section_name,
                f'{vm:.3f}' if vm else '',
                f'{sv:.4f}' if sv else '',
                f'{vc1:.3f}' if vc1 else '',
                f'{am:.2f}' if am else '',
                f'{sa:.3f}' if sa else '',
                f'{ac1:.2f}' if ac1 else '',
                homogeneity
            ])

        return pd.DataFrame(data)
