#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
KBZ Format Exporter
KBZ格式导出器

This module exports pile analysis data in KBZ format similar to KBZ1-9.TXT and KBZ1-9.xlsx
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
import pandas as pd
from pathlib import Path


class KBZExporter:
    """KBZ格式数据导出器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def export_to_txt(self, pile_data: Dict[str, Any], output_dir: str, pile_id: str) -> str:
        """
        导出为TXT格式文件（类似KBZ1-9.TXT）
        
        Args:
            pile_data: 桩基数据
            output_dir: 输出目录
            pile_id: 桩基ID
            
        Returns:
            导出文件路径
        """
        try:
            # 生成文件名
            pile_name = pile_data.get('Pile', pile_id)
            filename = f"{pile_name}.TXT"
            filepath = os.path.join(output_dir, filename)
            
            # 构建TXT内容
            content = self._build_txt_content(pile_data)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"TXT file exported: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to export TXT file: {str(e)}")
            raise
    
    def export_to_excel(self, pile_data: Dict[str, Any], output_dir: str, pile_id: str) -> str:
        """
        导出为Excel格式文件（类似KBZ1-9.xlsx）
        
        Args:
            pile_data: 桩基数据
            output_dir: 输出目录
            pile_id: 桩基ID
            
        Returns:
            导出文件路径
        """
        try:
            # 生成文件名
            pile_name = pile_data.get('Pile', pile_id)
            filename = f"{pile_name}.xlsx"
            filepath = os.path.join(output_dir, filename)
            
            # 创建Excel工作簿
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 桩信息表
                pile_info_df = self._create_pile_info_dataframe(pile_data)
                pile_info_df.to_excel(writer, sheet_name='桩信息', index=False, header=False)
                
                # 数据表
                data_df = self._create_data_dataframe(pile_data)
                data_df.to_excel(writer, sheet_name='数据表', index=False)
                
                # 单桩报告表
                report_df = self._create_report_dataframe(pile_data)
                report_df.to_excel(writer, sheet_name='单桩报告', index=False, header=False)
            
            self.logger.info(f"Excel file exported: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to export Excel file: {str(e)}")
            raise
    
    def _build_txt_content(self, pile_data: Dict[str, Any]) -> str:
        """构建TXT文件内容"""
        lines = []
        
        # 文件头部信息
        lines.append("")
        lines.append("/*********************基础数据**********************/")
        
        # 基本信息
        pile_name = pile_data.get('Pile', 'Unknown')
        sections = pile_data.get('Sections', 3)
        strength_grade = pile_data.get('StrengthGrade', 'C35')
        casting_date = pile_data.get('CastingDate', '2024-10-12')
        detecting_date = pile_data.get('DetectingDate', '2024-12-23')
        pile_length = pile_data.get('PileLength', 15.90)
        diameter = pile_data.get('Diameter', 1200)
        technical_code = pile_data.get('TechnicalCode', 'JGJ 106-2014')
        model = pile_data.get('Model', 'U5700')
        serial_no = pile_data.get('SerialNo', 'U72007005N')
        
        lines.extend([
            f"桩 基 名: {pile_name}",
            f"剖 面 数: {sections}",
            f"强度等级: {strength_grade}",
            f"浇筑日期: {casting_date}",
            f"检测日期: {detecting_date}",
            f"施工桩长: {pile_length}m",
            f"桩身外尺寸: {diameter}mm×{diameter}mm",
            f"检测规范: {technical_code}",
            f"仪器型号: {model}",
            f"仪器编号: {serial_no}",
            f"检定证书号: ",
            f"检测人员: ",
            f"检测人员上岗证号: ",
            f"GPS信息有效: 日期：{detecting_date} 时间：23:54:26  GPS坐标：0.00000000W, 0.00000000S",
            ""
        ])
        
        # 处理剖面数据
        sections_data = pile_data.get('Result_Sections_Data', [])
        test_data = pile_data.get('Test_Sections_Data', [])
        
        for i, section_data in enumerate(sections_data):
            lines.extend(self._build_section_content(section_data, i, test_data))
        
        # 版本信息
        lines.extend([
            f"软件版本: ZBLU5700 V1.0.0",
            f"检桩总根数量: {sections}",
            f"工程编号: 1223",
            f"工程地址: ",
            f"监理单位: ",
            f"施工单位: ",
            f"建设单位: ",
            f"监督单位: ",
            f"设计单位: ",
            f"勘察单位: ",
            f"委托单位: ",
            f"检测单位: ",
            f"检测单位资质证号: ",
            f"委托日期: {detecting_date}",
            f"检测日期: {detecting_date}",
            f"结构类型: ",
            f"桩基类型: ",
            ""
        ])
        
        # 原始数据记录
        lines.append("/*********************原始数据记录*********************/")
        lines.append("")
        
        # 添加原始测试数据
        if test_data:
            lines.extend(self._build_raw_data_content(test_data[0] if test_data else {}))
        
        return '\n'.join(lines)
    
    def _build_section_content(self, section_data: Dict, section_index: int, test_data: List) -> List[str]:
        """构建剖面内容"""
        lines = []
        
        # 剖面基本信息
        section_name = section_data.get('SectionName', f'{section_index+1}-{section_index+2}')
        distance = section_data.get('Distance', 470.0 + section_index * 150)
        length = section_data.get('Length', 15.90)
        
        lines.extend([
            f"剖面号: {section_index}",
            f"剖面组合: {section_name}",
            f"检测长度: {length}m",
            f"检测起始间距: 0.10m",
            f"剖面检测间距: {distance:.2f}mm",
            f"高程起始值: 0.00m",
            f"检测方向:正向",
            f"总测点数: 158",
            f"桩身检测长度: {length}m",
            f"PSD临界值: 200.00",
            f"临界值: 二倍",
            ""
        ])
        
        # 统计数据
        measurements = section_data.get('Measurements', [])
        if measurements:
            velocities = [m.get('Velocity', 0) for m in measurements if m.get('Velocity')]
            amplitudes = [m.get('Amplitude', 0) for m in measurements if m.get('Amplitude')]
            times = [m.get('Time', 0) for m in measurements if m.get('Time')]
            
            if velocities and amplitudes and times:
                # 计算统计值
                avg_velocity = sum(velocities) / len(velocities)
                avg_amplitude = sum(amplitudes) / len(amplitudes)
                avg_time = sum(times) / len(times)
                
                # 计算标准差（简化计算）
                vel_std = (sum((v - avg_velocity) ** 2 for v in velocities) / len(velocities)) ** 0.5
                amp_std = (sum((a - avg_amplitude) ** 2 for a in amplitudes) / len(amplitudes)) ** 0.5
                
                # 计算临界值
                vel_threshold = avg_velocity - 2 * vel_std
                amp_threshold = avg_amplitude - 2 * amp_std
                
                lines.extend([
                    f"声时临界值1: {avg_time:.3f}",
                    f"声时临界值2: 0.000",
                    f"声速临界值1: {vel_threshold:.3f}",
                    f"声速临界值2: 0.000",
                    f"频率临界值1: 0.000",
                    f"频率临界值2: 0.000",
                    f"贝塔系数1: 2.49",
                    f"贝塔系数3: 0.00",
                    f"声速平均值: {avg_velocity:.3f}",
                    f"声速标准差: {vel_std:.4f}",
                    f"幅度平均值: {avg_amplitude:.2f}",
                    f"幅度标准差: {amp_std:.3f}",
                    f"频率平均值: 0.00",
                    f"频率标准差: 0.000",
                    ""
                ])
        
        # 测量数据表头
        lines.append("深度(m)    声时(us)   声速(km/s) 幅度(dB)   频率(kHz)  PSD(us^2/cm) ")
        
        # 测量数据
        for measurement in measurements:
            depth = measurement.get('Depth', 0)
            time_val = measurement.get('Time', 0)
            velocity = measurement.get('Velocity', 0)
            amplitude = measurement.get('Amplitude', 0)
            frequency = measurement.get('Frequency', 0)
            psd = measurement.get('PSD', 0)
            
            lines.append(f"{depth:.2f}       {time_val:.2f}     {velocity:.3f}      {amplitude:.2f}     {frequency:.2f}       {psd:.3f}     ")
        
        lines.append("")
        return lines
    
    def _build_raw_data_content(self, test_section: Dict) -> List[str]:
        """构建原始数据内容"""
        lines = []
        
        # 测试参数
        lines.extend([
            f"剖面编号: 0",
            f"剖面组合: 1-2",
            f"总测点数: 158",
            f"剖面号: 0",
            f"测距: 470.00",
            f"采样点数: 1024",
            f"声波采样长度: 1024",
            f"增益设置: 0.40",
            f"激发电压: 250",
            f"延迟时间: 13.80",
            f"检测模式: 3",
            f"通道编号: 0",
            ""
        ])
        
        # 原始测量数据表头
        lines.append("****************声波测试数据**************")
        lines.append("测点号\t深度(mm)\t声时(us)\t幅度(dB)\t频率(kHz)")
        
        # 原始测量数据（示例数据）
        measurements = test_section.get('RawMeasurements', [])
        for i, measurement in enumerate(measurements[:158], 1):  # 限制158个测点
            depth = measurement.get('Depth', 0)
            time_val = measurement.get('Time', 0)
            amplitude = measurement.get('Amplitude', 0)
            frequency = measurement.get('Frequency', 0)
            
            lines.append(f"{i:03d}-01  \t{depth:.2f} \t{time_val:.2f}\t{amplitude:.2f}\t{frequency:.2f}  ")
        
        return lines
    
    def _create_pile_info_dataframe(self, pile_data: Dict[str, Any]) -> pd.DataFrame:
        """创建桩信息数据框"""
        data = [
            ['工程名称', '1223', '1223', '1223', '基桩名称', pile_data.get('Pile', 'Unknown')],
            ['设计桩长', f"{pile_data.get('PileLength', 15.90)}m", '设计桩径', f"{pile_data.get('Diameter', 1200)} mm", '设计强度', pile_data.get('StrengthGrade', 'C35')],
            ['浇筑日期', pile_data.get('CastingDate', '2024年10月12日'), '测试日期', pile_data.get('DetectingDate', '2024年12月23日'), '检测依据', pile_data.get('TechnicalCode', 'JGJ 106-2014')],
            ['仪器型号', pile_data.get('Model', 'U5700'), '仪器编号', pile_data.get('SerialNo', 'U72007005N'), '检定证号', ''],
            ['测试人员', '', '', '上岗证号', '', '']
        ]
        
        return pd.DataFrame(data)
    
    def _create_data_dataframe(self, pile_data: Dict[str, Any]) -> pd.DataFrame:
        """创建数据表数据框"""
        sections_data = pile_data.get('Result_Sections_Data', [])
        
        if not sections_data:
            return pd.DataFrame()
        
        # 构建表头
        columns = ['深度(m)']
        section_names = []
        
        for i, section in enumerate(sections_data[:3]):  # 最多3个剖面
            section_name = section.get('SectionName', f'{i+1}-{i+2}')
            distance = section.get('Distance', 470 + i * 150)
            section_names.append(f'{section_name}')
            
            columns.extend([f'波速(km/s)', f'幅度(dB)', f'声时(us)', f'PSD(us^2/cm)'])
        
        # 构建数据
        data = []
        
        # 添加剖面信息行
        header_row = ['']
        for i, section in enumerate(sections_data[:3]):
            section_name = section.get('SectionName', f'{i+1}-{i+2}')
            distance = section.get('Distance', 470 + i * 150)
            header_row.extend([f'剖面', section_name, f'测距', f'{distance}mm'])
        data.append(header_row)
        
        # 添加列标题行
        col_header = ['']
        for _ in range(len(sections_data[:3])):
            col_header.extend(['声速(km/s)', '幅度(dB)', '', ''])
        data.append(col_header)
        
        # 添加统计数据
        stats_rows = [
            ['最大值'] + self._get_section_stats(sections_data, 'max'),
            ['最小值'] + self._get_section_stats(sections_data, 'min'),
            ['平均值'] + self._get_section_stats(sections_data, 'mean'),
            ['标准差'] + self._get_section_stats(sections_data, 'std'),
            ['离差'] + self._get_section_stats(sections_data, 'deviation'),
            ['临界值'] + self._get_section_stats(sections_data, 'threshold'),
            [''],
            ['深度(m)', '波速(km/s)', '幅度(dB)', '声时(us)', 'PSD(us^2/cm)', '波速(km/s)', '幅度(dB)', '声时(us)', 'PSD(us^2/cm)', '波速(km/s)', '幅度(dB)', '声时(us)', 'PSD(us^2/cm)']
        ]
        
        data.extend(stats_rows)
        
        # 添加测量数据
        max_measurements = max(len(section.get('Measurements', [])) for section in sections_data) if sections_data else 0
        
        for i in range(max_measurements):
            row = [f'{(i + 1) * 0.1:.2f}']  # 深度
            
            for section in sections_data[:3]:
                measurements = section.get('Measurements', [])
                if i < len(measurements):
                    measurement = measurements[i]
                    row.extend([
                        measurement.get('Velocity', 0),
                        measurement.get('Amplitude', 0),
                        measurement.get('Time', 0),
                        measurement.get('PSD', 0)
                    ])
                else:
                    row.extend(['', '', '', ''])
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    def _get_section_stats(self, sections_data: List[Dict], stat_type: str) -> List:
        """获取剖面统计数据"""
        stats = []
        
        for section in sections_data[:3]:
            measurements = section.get('Measurements', [])
            if not measurements:
                stats.extend(['', '', '', ''])
                continue
            
            velocities = [m.get('Velocity', 0) for m in measurements if m.get('Velocity')]
            amplitudes = [m.get('Amplitude', 0) for m in measurements if m.get('Amplitude')]
            
            if stat_type == 'max':
                stats.extend([max(velocities) if velocities else '', max(amplitudes) if amplitudes else '', '', ''])
            elif stat_type == 'min':
                stats.extend([min(velocities) if velocities else '', min(amplitudes) if amplitudes else '', '', ''])
            elif stat_type == 'mean':
                stats.extend([
                    sum(velocities) / len(velocities) if velocities else '',
                    sum(amplitudes) / len(amplitudes) if amplitudes else '',
                    '', ''
                ])
            elif stat_type == 'std':
                if velocities:
                    vel_mean = sum(velocities) / len(velocities)
                    vel_std = (sum((v - vel_mean) ** 2 for v in velocities) / len(velocities)) ** 0.5
                else:
                    vel_std = ''
                
                if amplitudes:
                    amp_mean = sum(amplitudes) / len(amplitudes)
                    amp_std = (sum((a - amp_mean) ** 2 for a in amplitudes) / len(amplitudes)) ** 0.5
                else:
                    amp_std = ''
                
                stats.extend([vel_std, amp_std, '', ''])
            else:
                stats.extend(['', '', '', ''])
        
        return stats
    
    def _create_report_dataframe(self, pile_data: Dict[str, Any]) -> pd.DataFrame:
        """创建单桩报告数据框"""
        pile_name = pile_data.get('Pile', 'Unknown')
        sections = pile_data.get('Sections', 3)
        
        data = [
            ['桩基名称', pile_name, '', '', '', '', '', ''],
            ['剖面数量', str(sections), '', '', '', '', '', ''],
            ['检测长度', f"{pile_data.get('PileLength', 15.90)}m", '', '', '', '', '', ''],
            ['检测结果', '合格', '', '', '', '', '', ''],
            ['', '', '', '', '', '', '', ''],
            ['剖面组合', '声速平均值', '声速标准差', '声速临界值', '幅度平均值', '幅度标准差', '幅度临界值', '检测结果'],
        ]
        
        # 添加各剖面结果
        sections_data = pile_data.get('Result_Sections_Data', [])
        for section in sections_data:
            measurements = section.get('Measurements', [])
            if measurements:
                velocities = [m.get('Velocity', 0) for m in measurements if m.get('Velocity')]
                amplitudes = [m.get('Amplitude', 0) for m in measurements if m.get('Amplitude')]
                
                if velocities and amplitudes:
                    vel_mean = sum(velocities) / len(velocities)
                    amp_mean = sum(amplitudes) / len(amplitudes)
                    vel_std = (sum((v - vel_mean) ** 2 for v in velocities) / len(velocities)) ** 0.5
                    amp_std = (sum((a - amp_mean) ** 2 for a in amplitudes) / len(amplitudes)) ** 0.5
                    
                    section_name = section.get('SectionName', 'Unknown')
                    data.append([
                        section_name,
                        f'{vel_mean:.3f}',
                        f'{vel_std:.4f}',
                        f'{vel_mean - 2 * vel_std:.3f}',
                        f'{amp_mean:.2f}',
                        f'{amp_std:.3f}',
                        f'{amp_mean - 2 * amp_std:.2f}',
                        '合格'
                    ])
        
        return pd.DataFrame(data)
