#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API Client for Pile Analysis System
桩基分析系统API客户端

This module provides HTTP client functionality for fetching pile data
from the remote API endpoint.
"""

import json
import time
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from ..utils.config_manager import ConfigManager
from ..utils.error_handler import APIError, ValidationError


class PileAPIClient:
    """
    桩基数据API客户端
    
    Handles HTTP requests to the pile analysis API endpoints,
    including data fetching, validation, and error handling.
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化API客户端
        
        Args:
            config_path: 配置文件路径
        """
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_api_config()
        self.logger = logging.getLogger(__name__)
        
        # 初始化HTTP会话
        self.session = self._create_session()
        
        # 缓存设置
        self.cache = {}
        self.cache_enabled = self.config.get('cache_settings', {}).get('enabled', True)
        self.cache_duration = timedelta(
            minutes=self.config.get('cache_settings', {}).get('cache_duration_minutes', 30)
        )
        
        # 速率限制
        self.last_request_time = None
        self.rate_limit = self.config.get('request_settings', {}).get('rate_limit_per_minute', 60)
        
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=self.config.get('api_endpoints', {}).get('pile_data', {}).get('retry_attempts', 3),
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=self.config.get('request_settings', {}).get('connection_pool_size', 10),
            pool_maxsize=self.config.get('request_settings', {}).get('connection_pool_size', 10)
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _apply_rate_limit(self):
        """应用速率限制"""
        if self.last_request_time:
            time_since_last = time.time() - self.last_request_time
            min_interval = 60.0 / self.rate_limit
            
            if time_since_last < min_interval:
                sleep_time = min_interval - time_since_last
                self.logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _get_cache_key(self, pile_id: str) -> str:
        """生成缓存键"""
        return f"pile_data_{pile_id}"
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        if not self.cache_enabled:
            return False
            
        timestamp = cache_entry.get('timestamp')
        if not timestamp:
            return False
            
        return datetime.now() - timestamp < self.cache_duration
    
    def get_pile_data(self, pile_id: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        获取桩基数据
        
        Args:
            pile_id: 桩基ID
            use_cache: 是否使用缓存
            
        Returns:
            桩基数据字典
            
        Raises:
            APIError: API请求失败
            ValidationError: 数据验证失败
        """
        # 检查缓存
        cache_key = self._get_cache_key(pile_id)
        if use_cache and cache_key in self.cache:
            cache_entry = self.cache[cache_key]
            if self._is_cache_valid(cache_entry):
                self.logger.info(f"Using cached data for pile {pile_id}")
                return cache_entry['data']
        
        # 应用速率限制
        self._apply_rate_limit()
        
        # 准备请求
        endpoint_config = self.config['api_endpoints']['pile_data']
        url = endpoint_config['url']
        headers = endpoint_config.get('headers', {})
        timeout = endpoint_config.get('timeout', 30)
        
        payload = {"id": pile_id}
        
        try:
            self.logger.info(f"Fetching pile data for ID: {pile_id}")
            
            response = self.session.post(
                url=url,
                json=payload,
                headers=headers,
                timeout=timeout
            )
            
            response.raise_for_status()
            
            # 解析响应
            data = response.json()
            
            # 验证响应格式
            if not self._validate_response(data):
                raise ValidationError(f"Invalid response format for pile {pile_id}")
            
            # 提取实际数据
            pile_data = data.get('data', {})
            
            # 验证桩基数据
            if not self._validate_pile_data(pile_data):
                raise ValidationError(f"Invalid pile data for pile {pile_id}")
            
            # 缓存数据
            if self.cache_enabled:
                self.cache[cache_key] = {
                    'data': pile_data,
                    'timestamp': datetime.now()
                }
            
            self.logger.info(f"Successfully fetched pile data for ID: {pile_id}")
            return pile_data
            
        except requests.exceptions.RequestException as e:
            error_msg = f"API request failed for pile {pile_id}: {str(e)}"
            self.logger.error(error_msg)
            raise APIError(error_msg) from e
        
        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse JSON response for pile {pile_id}: {str(e)}"
            self.logger.error(error_msg)
            raise APIError(error_msg) from e
    
    def _validate_response(self, response_data: Dict) -> bool:
        """验证API响应格式"""
        required_fields = ['message', 'code', 'data']
        
        for field in required_fields:
            if field not in response_data:
                self.logger.error(f"Missing required field in response: {field}")
                return False
        
        # 检查响应状态
        if response_data.get('code') != '0':
            self.logger.error(f"API returned error code: {response_data.get('code')}")
            return False
        
        return True
    
    def _validate_pile_data(self, pile_data: Dict) -> bool:
        """验证桩基数据"""
        validation_config = self.config.get('data_validation', {})
        required_fields = validation_config.get('required_fields', [])
        
        # 检查必需字段
        for field in required_fields:
            if field not in pile_data:
                self.logger.error(f"Missing required field in pile data: {field}")
                return False
        
        # 验证数据结构
        if validation_config.get('validate_data_structure', True):
            if not self._validate_data_structure(pile_data):
                return False
        
        return True
    
    def _validate_data_structure(self, pile_data: Dict) -> bool:
        """验证数据结构"""
        # 验证剖面数据
        sections_data = pile_data.get('Result_Sections_Data')
        if sections_data and isinstance(sections_data, list):
            for section in sections_data:
                if not isinstance(section, dict):
                    self.logger.error("Invalid section data structure")
                    return False
        
        # 验证测试数据
        test_data = pile_data.get('Test_Sections_Data')
        if test_data and isinstance(test_data, list):
            for test_section in test_data:
                if not isinstance(test_section, dict):
                    self.logger.error("Invalid test section data structure")
                    return False
        
        return True
    
    def get_multiple_piles(self, pile_ids: List[str], max_concurrent: int = None) -> Dict[str, Dict]:
        """
        批量获取多个桩基数据
        
        Args:
            pile_ids: 桩基ID列表
            max_concurrent: 最大并发数
            
        Returns:
            桩基数据字典，键为桩基ID
        """
        if max_concurrent is None:
            max_concurrent = self.config.get('request_settings', {}).get('max_concurrent_requests', 5)
        
        results = {}
        failed_ids = []
        
        for pile_id in pile_ids:
            try:
                results[pile_id] = self.get_pile_data(pile_id)
            except (APIError, ValidationError) as e:
                self.logger.error(f"Failed to fetch data for pile {pile_id}: {str(e)}")
                failed_ids.append(pile_id)
        
        if failed_ids:
            self.logger.warning(f"Failed to fetch data for {len(failed_ids)} piles: {failed_ids}")
        
        return results
    
    def clear_cache(self):
        """清除缓存"""
        self.cache.clear()
        self.logger.info("Cache cleared")
    
    def close(self):
        """关闭客户端"""
        if self.session:
            self.session.close()
        self.logger.info("API client closed")
