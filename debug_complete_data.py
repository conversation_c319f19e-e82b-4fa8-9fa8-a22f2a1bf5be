#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Debug Complete Data Export
调试完整数据导出

This script debugs why the complete data is not being exported correctly.
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from test_complete_kbz_export import create_complete_sample_data
from src.data_processing.kbz_exporter import KB<PERSON>Exporter


def debug_complete_data():
    """调试完整数据导出"""
    print("Debugging Complete Data Export...")
    print("=" * 60)
    
    # 创建完整示例数据
    pile_data = create_complete_sample_data()
    
    print("Data Structure Analysis:")
    print(f"Pile: {pile_data['Pile']}")
    print(f"Sections: {pile_data['Sections']}")
    print(f"Result_Sections_Data count: {len(pile_data['Result_Sections_Data'])}")
    
    for i, section in enumerate(pile_data['Result_Sections_Data']):
        print(f"\nSection {i+1} ({section['SectionName']}):")
        print(f"  TotalPoints: {section['TotalPoints']}")
        print(f"  Data points: {len(section['data'])}")
        print(f"  First data point: H={section['data'][0]['H']}, V={section['data'][0]['V']}")
        print(f"  Last data point: H={section['data'][-1]['H']}, V={section['data'][-1]['V']}")
    
    print(f"\nTest_Sections_Data count: {len(pile_data['Test_Sections_Data'])}")
    
    for i, test_section in enumerate(pile_data['Test_Sections_Data']):
        print(f"\nTest Section {i+1} ({test_section['Object']}):")
        print(f"  TotalPoints: {test_section['TotalPoints']}")
        print(f"  UltraData points: {len(test_section['UltraData'])}")
        print(f"  First ultra point: SEQ={test_section['UltraData'][0]['SEQ']}, H={test_section['UltraData'][0]['H']}")
        print(f"  Last ultra point: SEQ={test_section['UltraData'][-1]['SEQ']}, H={test_section['UltraData'][-1]['H']}")
        print(f"  Wave data length: {len(test_section['UltraData'][0]['WaveData'])}")
    
    # 测试导出器
    print("\n" + "=" * 60)
    print("Testing KBZ Exporter...")
    
    exporter = KBZExporter()
    
    # 创建输出目录
    output_dir = "./debug_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 导出TXT文件
    txt_file = exporter.export_to_txt(pile_data, output_dir, "DEBUG")
    print(f"TXT file exported: {txt_file}")
    
    # 检查TXT文件内容
    with open(txt_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"TXT file size: {len(content)} characters")
    
    # 计算声波测试数据部分的测点数
    test_data_start = content.find("****************声波测试数据**************")
    if test_data_start != -1:
        test_data_section = content[test_data_start:]
        wave_data_start = test_data_section.find("****************各测点声参量及波形**************")
        if wave_data_start != -1:
            test_data_only = test_data_section[:wave_data_start]
        else:
            test_data_only = test_data_section
        
        # 计算测点数
        point_count = test_data_only.count("-01  \t")
        print(f"声波测试数据测点数: {point_count}")
    
    # 计算各测点声参量及波形部分的测点数
    wave_data_start = content.find("****************各测点声参量及波形**************")
    if wave_data_start != -1:
        wave_data_section = content[wave_data_start:]
        wave_point_count = wave_data_section.count("波形放大倍数:")
        print(f"各测点声参量及波形测点数: {wave_point_count}")
    
    # 导出Excel文件
    excel_file = exporter.export_to_excel(pile_data, output_dir, "DEBUG")
    print(f"Excel file exported: {excel_file}")
    
    # 检查Excel文件
    import pandas as pd
    data_sheet = pd.read_excel(excel_file, sheet_name='数据表')
    print(f"Excel data sheet shape: {data_sheet.shape}")
    
    # 计算实际数据行数（排除标题行）
    data_rows = 0
    for i, row in data_sheet.iterrows():
        if i >= 10:  # 跳过标题行
            if pd.notna(row.iloc[0]) and str(row.iloc[0]).replace('.', '').isdigit():
                data_rows += 1
    
    print(f"Excel实际数据行数: {data_rows}")
    
    print("\n" + "=" * 60)
    print("Debug Complete!")


if __name__ == "__main__":
    debug_complete_data()
