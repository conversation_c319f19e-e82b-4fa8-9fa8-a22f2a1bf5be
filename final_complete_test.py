#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Final Complete Test for KBZ Export
最终完整的KBZ导出测试

This script creates and tests the complete KBZ export with all 159 measurement points
from 0.10m to 15.90m for all 3 sections.
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_processing.kbz_exporter import KBZExporter
from src.utils.logger import setup_logging


def create_final_complete_data():
    """创建最终完整的测试数据"""
    import random
    
    # 生成完整的159个测点数据（从0.10m到15.90m）
    def generate_section_data(section_name, base_velocity, base_amplitude, pipe_dist):
        measurements = []
        for i in range(159):  # 159个测点
            depth = 0.10 + i * 0.10  # 从0.10m开始，间隔0.10m
            
            # 添加随机变化
            velocity_variation = random.uniform(-0.3, 0.3)
            amplitude_variation = random.uniform(-10, 10)
            time_variation = random.uniform(-10, 10)
            
            measurement = {
                "SEQ": i + 1,
                "H": depth,
                "T": 120.0 + time_variation,
                "V": base_velocity + velocity_variation,
                "A": base_amplitude + amplitude_variation,
                "F": 0.00,
                "P": random.uniform(0.0, 2.0),
                "VNormFlag": 0,
                "ANormFlag": 0,
                "FNormFlag": 0,
                "PNormFlag": 0,
                "FlawFlag": 0,
                "nValidFlag": 1
            }
            measurements.append(measurement)
        
        # 计算统计值
        velocities = [m["V"] for m in measurements]
        amplitudes = [m["A"] for m in measurements]
        times = [m["T"] for m in measurements]
        
        vm = sum(velocities) / len(velocities)
        am = sum(amplitudes) / len(amplitudes)
        tm = sum(times) / len(times)
        sv = (sum((v - vm) ** 2 for v in velocities) / len(velocities)) ** 0.5
        sa = (sum((a - am) ** 2 for a in amplitudes) / len(amplitudes)) ** 0.5
        
        return {
            "SectionName": section_name,
            "InitialDepth": 0.10,
            "Interval": 0.10,
            "PipeDist": pipe_dist,
            "HeightCorrection": 0.00,
            "Direction": "正向",
            "nDirection": 1,
            "TotalPoints": 159,
            "TestedLength": 15.90,
            "CriticOfPsd": 200.00,
            "CriticalValue": "二倍",
            "Vc1": vm - 2 * sv,
            "Vc2": 0.000,
            "Ac1": am - 2 * sa,
            "Ac2": 0.000,
            "Fc1": 0.000,
            "Fc2": 0.000,
            "Coefficient1": 2.49,
            "Coefficient3": 0.00,
            "Vm": vm,
            "Sv": sv,
            "Am": am,
            "Sa": sa,
            "Fm": 0.00,
            "Sf": 0.000,
            "CveSpeed": sv / vm if vm > 0 else 0,
            "CveAmp": sa / am if am > 0 else 0,
            "fAveTime": tm,
            "data": measurements
        }
    
    # 生成完整的超声数据
    def generate_ultra_data(section_name, total_points=159):
        ultra_data = []
        for i in range(total_points):
            depth = 15.90 - i * 0.10  # 从桩底开始向上
            
            # 生成1024个波形数据点
            wave_data = []
            for j in range(1024):
                if j < 50:
                    wave_data.append(2000 + random.randint(-50, 50))
                elif j < 200:
                    peak_factor = 1 + 2 * (1 - abs(j - 125) / 75)
                    wave_data.append(int(2000 * peak_factor) + random.randint(-100, 100))
                else:
                    decay_factor = max(0.1, 1 - (j - 200) / 800)
                    wave_data.append(int(2000 * decay_factor) + random.randint(-50, 50))
            
            ultra_measurement = {
                "SEQ": i + 1,
                "T": 100.0 + random.uniform(-10, 10),
                "orgTime": 100.0 + random.uniform(-10, 10),
                "A": 130.0 + random.uniform(-15, 15),
                "F": 0.00,
                "fDist": depth,
                "validFlag": 1,
                "cName": f"{i+1:03d}-01",
                "fOrgA": 130.0 + random.uniform(-15, 15),
                "PSD": random.uniform(0.0, 2.0),
                "WaveVelocity": 4.0 + random.uniform(-0.5, 0.5),
                "H": depth,
                "Amplification": random.randint(65, 80),
                "Baseline": 0,
                "Delay": random.randint(300, 450),
                "JudgeFlag": 0,
                "deleteFlag": 0,
                "Gain": random.randint(65, 80),
                "Start": 100,
                "FirstPeak": 150,
                "CursorX": 200,
                "CursorY": 300,
                "BaseCor": 0,
                "VNormFlag": 0,
                "ANormFlag": 0,
                "FNormFlag": 0,
                "PNormFlag": 0,
                "FlawFlag": 0,
                "Integrity": 1.0,
                "RealDist": depth,
                "WaveData": wave_data
            }
            ultra_data.append(ultra_measurement)
        
        return ultra_data
    
    # 创建3个完整的截面数据
    sections_info = [
        {"name": "1-2", "distance": 470.0, "base_velocity": 4.127, "base_amplitude": 138.96},
        {"name": "1-3", "distance": 770.0, "base_velocity": 4.228, "base_amplitude": 131.98},
        {"name": "2-3", "distance": 800.0, "base_velocity": 4.500, "base_amplitude": 131.60}
    ]
    
    result_sections_data = []
    test_sections_data = []
    
    for i, section_info in enumerate(sections_info):
        # 生成Result_Sections_Data
        section_data = generate_section_data(
            section_info["name"],
            section_info["base_velocity"],
            section_info["base_amplitude"],
            section_info["distance"]
        )
        section_data["SectionNo"] = i
        result_sections_data.append(section_data)
        
        # 生成Test_Sections_Data
        ultra_data = generate_ultra_data(section_info["name"])
        test_section = {
            "ObjectNo": i,
            "Object": section_info["name"],
            "TotalPoints": 159,
            "MaxSubNo": 1,
            "Dist": section_info["distance"],
            "SamplingLength": 1024,
            "SavedLength": 1024,
            "SamplingInterval": 0.40,
            "TrigVoltage": 250,
            "InitialTime": 13.80,
            "TriggerMode": 3,
            "Channel": 0,
            "UltraData": ultra_data
        }
        test_sections_data.append(test_section)
    
    return {
        "Pile": "KBZ1-9",
        "Sections": 3,
        "StrengthGrade": "C35",
        "CastingDate": "2024年10月12日",
        "DetectingDate": "2024年12月23日 15:54:26",
        "PileLength": 15.90,
        "Diameter": 1200.0,
        "TechnicalCode": "JGJ 106-2014",
        "Model": "U5700",
        "SerialNo": "U72007005N",
        "Version": "ZBLU5700 V1.0.0",
        "TotalObjects": 3,
        "Project": "1223",
        "Address": "",
        "DesignUnit": "",
        "ConstructionUnit": "",
        "BuildingUnit": "",
        "SupervisionUnit": "",
        "MonitoringUnit": "",
        "EntrustingUnit": "",
        "DetectionUnit": "",
        "EntrustingDate": "2024年12月23日",
        "CalculationDate": "2024年12月23日",
        "Result_Sections_Data": result_sections_data,
        "Test_Sections_Data": test_sections_data
    }


def main():
    """主测试函数"""
    print("Final Complete KBZ Export Test")
    print("最终完整的KBZ导出测试")
    print("=" * 60)
    
    try:
        # 设置日志
        setup_logging()
        
        # 创建完整数据
        pile_data = create_final_complete_data()
        print(f"✓ Created complete data for pile: {pile_data['Pile']}")
        print(f"✓ Total sections: {len(pile_data['Result_Sections_Data'])}")
        
        total_measurements = 0
        for i, section in enumerate(pile_data['Result_Sections_Data']):
            measurements_count = len(section['data'])
            total_measurements += measurements_count
            print(f"  Section {i+1} ({section['SectionName']}): {measurements_count} measurements")
        
        print(f"✓ Total measurement points: {total_measurements}")
        
        # 初始化导出器
        exporter = KBZExporter()
        print("✓ KBZ exporter initialized")
        
        # 创建输出目录
        output_dir = "./final_complete_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 导出TXT文件
        print("\nExporting TXT file...")
        txt_file = exporter.export_to_txt(pile_data, output_dir, "COMPLETE-KBZ")
        print(f"✓ TXT file exported: {txt_file}")
        
        # 检查TXT文件
        with open(txt_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"  File size: {len(content):,} characters")
        
        # 计算测点数
        test_points = content.count("-01  \t")
        wave_points = content.count("波形放大倍数:")
        
        print(f"  声波测试数据测点数: {test_points}")
        print(f"  各测点声参量及波形测点数: {wave_points}")
        
        # 导出Excel文件
        print("\nExporting Excel file...")
        excel_file = exporter.export_to_excel(pile_data, output_dir, "COMPLETE-KBZ")
        print(f"✓ Excel file exported: {excel_file}")
        
        # 检查Excel文件
        import pandas as pd
        data_sheet = pd.read_excel(excel_file, sheet_name='数据表')
        print(f"  Excel data sheet shape: {data_sheet.shape}")
        
        print("\n" + "=" * 60)
        print("FINAL COMPLETE TEST RESULTS:")
        print(f"✓ TXT文件: {len(content):,} 字符")
        print(f"✓ 声波测试数据: {test_points} 个测点")
        print(f"✓ 波形数据: {wave_points} 个测点")
        print(f"✓ Excel文件: {data_sheet.shape[0]} 行 x {data_sheet.shape[1]} 列")
        print(f"✓ 预期数据点: 159 测点 x 3 截面 = 477 总数据点")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
