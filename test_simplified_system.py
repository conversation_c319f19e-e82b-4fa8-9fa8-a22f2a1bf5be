#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test Script for Simplified Pile Analysis System
简化桩基分析系统测试脚本

This script tests the simplified system functionality including API connection and data export.
"""

import sys
import os
import json
import logging
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_acquisition.api_client import PileAP<PERSON>lient
from src.data_processing.kbz_exporter import KBZExporter
from src.utils.logger import setup_logging


def create_sample_data():
    """创建示例数据用于测试"""
    return {
        "Pile": "TEST-001",
        "Sections": 3,
        "StrengthGrade": "C35",
        "CastingDate": "2024-10-12",
        "DetectingDate": "2024-12-23",
        "PileLength": 15.90,
        "Diameter": 1200,
        "TechnicalCode": "JGJ 106-2014",
        "Model": "U5700",
        "SerialNo": "U72007005N",
        "Result_Sections_Data": [
            {
                "SectionName": "1-2",
                "Distance": 470.0,
                "Length": 15.90,
                "Measurements": [
                    {
                        "Depth": 0.20,
                        "Time": 123.40,
                        "Velocity": 3.809,
                        "Amplitude": 115.77,
                        "Frequency": 0.00,
                        "PSD": 1.600
                    },
                    {
                        "Depth": 0.30,
                        "Time": 127.40,
                        "Velocity": 3.689,
                        "Amplitude": 115.42,
                        "Frequency": 0.00,
                        "PSD": 0.000
                    },
                    {
                        "Depth": 0.40,
                        "Time": 127.40,
                        "Velocity": 3.689,
                        "Amplitude": 122.29,
                        "Frequency": 0.00,
                        "PSD": 7.056
                    }
                ]
            },
            {
                "SectionName": "1-3",
                "Distance": 770.0,
                "Length": 15.90,
                "Measurements": [
                    {
                        "Depth": 0.20,
                        "Time": 181.40,
                        "Velocity": 4.245,
                        "Amplitude": 124.18,
                        "Frequency": 0.00,
                        "PSD": 0.576
                    },
                    {
                        "Depth": 0.30,
                        "Time": 179.00,
                        "Velocity": 4.302,
                        "Amplitude": 125.55,
                        "Frequency": 0.00,
                        "PSD": 0.576
                    }
                ]
            },
            {
                "SectionName": "2-3",
                "Distance": 800.0,
                "Length": 15.90,
                "Measurements": [
                    {
                        "Depth": 0.20,
                        "Time": 171.10,
                        "Velocity": 4.676,
                        "Amplitude": 111.49,
                        "Frequency": 0.00,
                        "PSD": 0.400
                    }
                ]
            }
        ],
        "Test_Sections_Data": [
            {
                "SectionIndex": 0,
                "SectionName": "1-2",
                "RawMeasurements": [
                    {
                        "Depth": 15.90,
                        "Time": 107.80,
                        "Amplitude": 139.44,
                        "Frequency": 0.00
                    },
                    {
                        "Depth": 15.80,
                        "Time": 105.40,
                        "Amplitude": 138.17,
                        "Frequency": 0.00
                    }
                ]
            }
        ]
    }


def test_api_connection():
    """测试API连接"""
    print("Testing API connection...")
    
    try:
        api_client = PileAPIClient()
        print("✓ API client initialized successfully")
        
        # 测试获取数据（使用默认测试ID）
        pile_id = "512856130"
        print(f"Attempting to fetch data for pile ID: {pile_id}")
        
        pile_data = api_client.get_pile_data(pile_id)
        print("✓ API data fetched successfully")
        print(f"  Pile name: {pile_data.get('Pile', 'Unknown')}")
        print(f"  Sections: {pile_data.get('Sections', 'Unknown')}")
        
        api_client.close()
        return True, pile_data
        
    except Exception as e:
        print(f"✗ API connection failed: {str(e)}")
        print("Using sample data for testing...")
        return False, create_sample_data()


def test_kbz_export(pile_data, output_dir="./test_output"):
    """测试KBZ格式导出"""
    print("\nTesting KBZ export functionality...")
    
    try:
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 初始化导出器
        exporter = KBZExporter()
        print("✓ KBZ exporter initialized successfully")
        
        # 测试TXT导出
        txt_file = exporter.export_to_txt(pile_data, output_dir, "TEST-001")
        print(f"✓ TXT file exported: {txt_file}")
        
        # 测试Excel导出
        excel_file = exporter.export_to_excel(pile_data, output_dir, "TEST-001")
        print(f"✓ Excel file exported: {excel_file}")
        
        return True, [txt_file, excel_file]
        
    except Exception as e:
        print(f"✗ KBZ export failed: {str(e)}")
        return False, []


def test_file_contents(exported_files):
    """测试导出文件内容"""
    print("\nTesting exported file contents...")
    
    for file_path in exported_files:
        if not os.path.exists(file_path):
            print(f"✗ File not found: {file_path}")
            continue
        
        file_size = os.path.getsize(file_path)
        print(f"✓ File exists: {os.path.basename(file_path)} ({file_size} bytes)")
        
        if file_path.endswith('.TXT'):
            # 检查TXT文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "基础数据" in content and "桩 基 名" in content:
                        print("  ✓ TXT file contains expected KBZ format headers")
                    else:
                        print("  ✗ TXT file missing expected KBZ format content")
            except Exception as e:
                print(f"  ✗ Error reading TXT file: {str(e)}")
        
        elif file_path.endswith('.xlsx'):
            # 检查Excel文件内容
            try:
                import pandas as pd
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                expected_sheets = ['桩信息', '数据表', '单桩报告']
                
                if all(sheet in sheet_names for sheet in expected_sheets):
                    print("  ✓ Excel file contains expected sheets")
                else:
                    print(f"  ✗ Excel file missing expected sheets. Found: {sheet_names}")
                    
            except Exception as e:
                print(f"  ✗ Error reading Excel file: {str(e)}")


def main():
    """主测试函数"""
    print("=" * 60)
    print("Simplified Pile Analysis System Test")
    print("简化桩基分析系统测试")
    print("=" * 60)
    
    # 设置日志
    setup_logging()
    
    # 测试API连接
    api_success, pile_data = test_api_connection()
    
    # 测试KBZ导出
    export_success, exported_files = test_kbz_export(pile_data)
    
    # 测试文件内容
    if export_success and exported_files:
        test_file_contents(exported_files)
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"API Connection: {'✓ PASS' if api_success else '✗ FAIL (using sample data)'}")
    print(f"KBZ Export: {'✓ PASS' if export_success else '✗ FAIL'}")
    
    if export_success:
        print(f"Exported files: {len(exported_files)}")
        for file_path in exported_files:
            print(f"  - {file_path}")
    
    print("=" * 60)
    
    # 返回测试结果
    return export_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
