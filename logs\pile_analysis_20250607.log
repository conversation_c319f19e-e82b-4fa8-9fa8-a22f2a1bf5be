2025-06-07 08:55:22 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 08:55:22 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 08:55:22 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 08:55:22 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 08:55:22 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 08:55:22 [    INFO] src.utils.config_manager:56 - Config<PERSON>anager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:55:22 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 08:55:22 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:55:22 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:55:22 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:55:22 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:55:47 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 08:55:47 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 08:55:47 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 08:55:47 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 08:55:47 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 08:55:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:55:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 08:55:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:55:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:55:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:55:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:56:19 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 08:56:19 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 08:56:19 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 08:56:19 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 08:56:19 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 08:56:19 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:56:19 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 08:56:19 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:56:19 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:56:19 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:56:19 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:57:47 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 08:57:47 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 08:57:47 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 08:57:47 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 08:57:47 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 08:57:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:57:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 08:57:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:57:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:57:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:57:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:58:08 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 08:58:08 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 08:58:08 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 08:58:08 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 08:58:08 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 08:58:08 [    INFO] __main__:82 - Starting CLI mode analysis
2025-06-07 08:58:08 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:58:08 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 08:58:08 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:58:08 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:58:08 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:58:08 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:58:45 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 08:58:45 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 08:58:45 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 08:58:45 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 08:58:45 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 08:58:45 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:58:45 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 08:58:45 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:58:45 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 08:58:45 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 08:58:45 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:02:08 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 09:02:08 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 09:02:08 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 09:02:08 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 09:02:08 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 09:02:08 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:02:08 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:02:08 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:02:08 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:02:08 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:02:08 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:02:08 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 09:02:08 [    INFO] src.gui.main_window:187 - System components initialized successfully
2025-06-07 09:02:13 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 09:02:23 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 09:02:23 [    INFO] src.data_processing.format_converter:103 - Converting API data to GZ analysis format
2025-06-07 09:02:23 [    INFO] src.data_processing.format_converter:130 - Successfully converted to GZ analysis format
2025-06-07 09:03:28 [    INFO] src.data_acquisition.api_client:128 - Using cached data for pile 512856130
2025-06-07 09:03:28 [    INFO] src.data_processing.format_converter:103 - Converting API data to GZ analysis format
2025-06-07 09:03:28 [    INFO] src.data_processing.format_converter:130 - Successfully converted to GZ analysis format
2025-06-07 09:03:28 [    INFO] src.data_acquisition.api_client:128 - Using cached data for pile 512856130
2025-06-07 09:03:29 [    INFO] src.data_processing.format_converter:103 - Converting API data to GZ analysis format
2025-06-07 09:03:29 [    INFO] src.data_processing.format_converter:130 - Successfully converted to GZ analysis format
2025-06-07 09:03:29 [    INFO] src.data_acquisition.api_client:128 - Using cached data for pile 512856130
2025-06-07 09:03:29 [    INFO] src.data_processing.format_converter:103 - Converting API data to GZ analysis format
2025-06-07 09:03:29 [    INFO] src.data_processing.format_converter:130 - Successfully converted to GZ analysis format
2025-06-07 09:03:32 [    INFO] src.analysis_integration.gz_integration:235 - Starting pile analysis for pile: 512856130
2025-06-07 09:03:32 [    INFO] src.analysis_integration.gz_integration:408 - Results saved: ./work\gz_analysis_512856130_20250607_090332.txt
2025-06-07 09:03:32 [    INFO] src.analysis_integration.gz_integration:408 - Results saved: ./work\gz_analysis_512856130_20250607_090332.json
2025-06-07 09:03:32 [    INFO] src.analysis_integration.gz_integration:408 - Results saved: ./work\gz_analysis_512856130_20250607_090332.csv
2025-06-07 09:03:32 [    INFO] src.analysis_integration.gz_integration:278 - Analysis completed successfully for pile: 512856130
2025-06-07 09:12:47 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 09:12:47 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 09:12:47 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 09:12:47 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 09:12:47 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 09:12:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:12:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:12:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:12:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:12:47 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:12:47 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:12:47 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from D:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 09:12:47 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 09:12:53 [    INFO] src.gui.data_loader_window:837 - Status: 正在初始化组件...
2025-06-07 09:12:53 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:12:53 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:12:53 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:12:53 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:12:53 [    INFO] src.gui.data_loader_window:837 - Status: 组件初始化完成
2025-06-07 09:12:53 [    INFO] src.gui.data_loader_window:309 - Data loader components initialized successfully
2025-06-07 09:12:53 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 09:13:01 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 09:13:01 [    INFO] src.gui.data_loader_window:837 - Status: 正在获取API数据...
2025-06-07 09:13:06 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 09:13:06 [    INFO] src.gui.data_loader_window:837 - Status: API数据获取完成
2025-06-07 09:13:06 [   ERROR] src.gui.data_loader_window:511 - 更新数据预览失败: 'str' object has no attribute 'keys'
2025-06-07 09:15:09 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 09:15:09 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 09:15:09 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 09:15:09 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 09:15:09 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 09:15:09 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:15:09 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:15:09 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:15:09 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:15:09 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:15:09 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:15:09 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 09:15:09 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 09:15:12 [    INFO] src.gui.data_loader_window:876 - Status: 正在初始化组件...
2025-06-07 09:15:12 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:15:12 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:15:12 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:15:12 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:15:12 [    INFO] src.gui.data_loader_window:876 - Status: 组件初始化完成
2025-06-07 09:15:12 [    INFO] src.gui.data_loader_window:309 - Data loader components initialized successfully
2025-06-07 09:15:12 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 09:15:13 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 09:15:13 [    INFO] src.gui.data_loader_window:876 - Status: 正在获取API数据...
2025-06-07 09:15:27 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 09:15:27 [    INFO] src.gui.data_loader_window:876 - Status: API数据获取完成
2025-06-07 09:15:27 [   ERROR] src.gui.data_loader_window:511 - 更新数据预览失败: 'str' object has no attribute 'keys'
2025-06-07 09:24:38 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 09:24:38 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 09:24:38 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 09:24:38 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 09:24:38 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 09:24:39 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:24:39 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:24:39 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:24:39 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:24:39 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:24:39 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:24:39 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from D:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 09:24:39 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 09:24:42 [    INFO] src.gui.data_loader_window:956 - Status: 正在初始化组件...
2025-06-07 09:24:42 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:24:42 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:24:42 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:24:42 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:24:42 [    INFO] src.gui.data_loader_window:956 - Status: 组件初始化完成
2025-06-07 09:24:42 [    INFO] src.gui.data_loader_window:309 - Data loader components initialized successfully
2025-06-07 09:24:42 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 09:24:44 [    INFO] src.gui.data_loader_window:956 - Status: 正在获取API数据...
2025-06-07 09:24:44 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 09:24:47 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 09:24:47 [    INFO] src.gui.data_loader_window:956 - Status: API数据获取完成
2025-06-07 09:24:47 [    INFO] src.gui.data_loader_window:507 - Data preview updated successfully
2025-06-07 09:27:14 [    INFO] src.gui.data_loader_window:956 - Status: 正在初始化组件...
2025-06-07 09:27:14 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:27:14 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:27:14 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:27:14 [    INFO] src.utils.config_manager:113 - Loaded configuration from: D:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:27:14 [    INFO] src.gui.data_loader_window:956 - Status: 组件初始化完成
2025-06-07 09:27:14 [    INFO] src.gui.data_loader_window:309 - Data loader components initialized successfully
2025-06-07 09:27:14 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 09:27:15 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 09:27:15 [    INFO] src.gui.data_loader_window:956 - Status: 正在获取API数据...
2025-06-07 09:27:19 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://*************/pilediag/pile/pileJson
2025-06-07 09:27:19 [    INFO] src.gui.data_loader_window:956 - Status: API数据获取失败: API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://*************/pilediag/pile/pileJson
2025-06-07 09:36:28 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 09:36:28 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 09:36:28 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 09:36:28 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 09:36:28 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 09:36:28 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:36:28 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:36:28 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:36:28 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:36:28 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:36:28 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:36:28 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 09:36:28 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 09:36:31 [    INFO] src.gui.data_loader_window:1325 - Status: 正在初始化组件...
2025-06-07 09:36:31 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:36:31 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 09:36:31 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 09:36:31 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 09:36:31 [    INFO] src.gui.data_loader_window:1325 - Status: 组件初始化完成
2025-06-07 09:36:31 [    INFO] src.gui.data_loader_window:401 - Data loader components initialized successfully
2025-06-07 09:36:31 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 09:36:33 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 09:36:33 [    INFO] src.gui.data_loader_window:1325 - Status: 正在获取API数据...
2025-06-07 09:36:36 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 09:36:36 [    INFO] src.gui.data_loader_window:1325 - Status: API数据获取完成
2025-06-07 09:36:36 [    INFO] src.gui.data_loader_window:605 - Data preview updated successfully
2025-06-07 10:04:49 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 10:04:49 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 10:04:49 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 10:04:49 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 10:04:49 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 10:04:49 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:04:49 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:04:49 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:04:49 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:04:49 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:04:49 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:04:49 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 10:04:49 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 10:04:54 [    INFO] src.gui.data_loader_window:2111 - Status: 正在初始化组件...
2025-06-07 10:04:54 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:04:54 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:04:54 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:04:54 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:04:54 [    INFO] src.gui.data_loader_window:2111 - Status: 组件初始化完成
2025-06-07 10:04:54 [    INFO] src.gui.data_loader_window:978 - Data loader components initialized successfully
2025-06-07 10:04:54 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:04:56 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:04:56 [    INFO] src.gui.data_loader_window:2111 - Status: 正在获取API数据...
2025-06-07 10:05:01 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://*************/pilediag/pile/pileJson
2025-06-07 10:05:01 [    INFO] src.gui.data_loader_window:2111 - Status: API数据获取失败: API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://*************/pilediag/pile/pileJson
2025-06-07 10:07:50 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 10:07:50 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 10:07:50 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 10:07:50 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 10:07:50 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 10:07:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:07:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:07:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:07:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:07:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:07:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:07:50 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 10:07:50 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 10:10:04 [    INFO] src.gui.data_loader_window:2111 - Status: 正在初始化组件...
2025-06-07 10:10:04 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:10:04 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:10:04 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:10:04 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:10:04 [    INFO] src.gui.data_loader_window:2111 - Status: 组件初始化完成
2025-06-07 10:10:04 [    INFO] src.gui.data_loader_window:978 - Data loader components initialized successfully
2025-06-07 10:10:04 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:10:05 [    INFO] src.gui.data_loader_window:2111 - Status: 正在获取API数据...
2025-06-07 10:10:05 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:10:10 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 10:10:10 [    INFO] src.gui.data_loader_window:2111 - Status: API数据获取完成
2025-06-07 10:10:10 [    INFO] src.gui.data_loader_window:1182 - Data preview updated successfully
2025-06-07 10:10:26 [   ERROR] src.gui.data_loader_window:557 - 截面分析失败: 'str' object has no attribute 'get'
2025-06-07 10:10:52 [    INFO] src.data_acquisition.api_client:128 - Using cached data for pile 512856130
2025-06-07 10:10:52 [    INFO] src.gui.data_loader_window:2111 - Status: 正在获取API数据...
2025-06-07 10:10:52 [    INFO] src.gui.data_loader_window:2111 - Status: API数据获取完成
2025-06-07 10:10:52 [   ERROR] src.gui.data_loader_window:1186 - 更新数据预览失败: invalid command name ".!toplevel.!frame.!labelframe2.!notebook.!frame4.!frame.!labelframe2.!notebook.!frame.!treeview"
2025-06-07 10:10:55 [    INFO] src.data_acquisition.api_client:128 - Using cached data for pile 512856130
2025-06-07 10:10:55 [    INFO] src.gui.data_loader_window:2111 - Status: 正在获取API数据...
2025-06-07 10:10:55 [    INFO] src.gui.data_loader_window:2111 - Status: API数据获取完成
2025-06-07 10:10:55 [   ERROR] src.gui.data_loader_window:1186 - 更新数据预览失败: invalid command name ".!toplevel.!frame.!labelframe2.!notebook.!frame4.!frame.!labelframe2.!notebook.!frame.!treeview"
2025-06-07 10:11:04 [    INFO] src.gui.data_loader_window:2111 - Status: 正在初始化组件...
2025-06-07 10:11:04 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:11:04 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:11:04 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:11:04 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:11:04 [    INFO] src.gui.data_loader_window:2111 - Status: 组件初始化完成
2025-06-07 10:11:04 [    INFO] src.gui.data_loader_window:978 - Data loader components initialized successfully
2025-06-07 10:11:04 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:11:07 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:11:07 [    INFO] src.gui.data_loader_window:2111 - Status: 正在获取API数据...
2025-06-07 10:11:16 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 10:11:16 [    INFO] src.gui.data_loader_window:2111 - Status: API数据获取完成
2025-06-07 10:11:16 [    INFO] src.gui.data_loader_window:1182 - Data preview updated successfully
2025-06-07 10:21:03 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 10:21:03 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 10:21:03 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 10:21:03 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 10:21:03 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 10:21:03 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:21:03 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:21:03 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:21:03 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:21:03 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:21:03 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:21:03 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 10:21:03 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 10:21:05 [    INFO] src.gui.data_loader_window:2111 - Status: 正在初始化组件...
2025-06-07 10:21:05 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:21:05 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:21:05 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:21:05 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:21:05 [    INFO] src.gui.data_loader_window:2111 - Status: 组件初始化完成
2025-06-07 10:21:05 [    INFO] src.gui.data_loader_window:978 - Data loader components initialized successfully
2025-06-07 10:21:05 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:21:08 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:21:08 [    INFO] src.gui.data_loader_window:2111 - Status: 正在获取API数据...
2025-06-07 10:21:24 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 10:21:24 [    INFO] src.gui.data_loader_window:2111 - Status: API数据获取完成
2025-06-07 10:21:24 [    INFO] src.gui.data_loader_window:1182 - Data preview updated successfully
2025-06-07 10:27:12 [    INFO] src.gui.data_loader_window:2111 - Status: 正在初始化组件...
2025-06-07 10:27:12 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:27:12 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:27:12 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:27:12 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:27:12 [    INFO] src.gui.data_loader_window:2111 - Status: 组件初始化完成
2025-06-07 10:27:12 [    INFO] src.gui.data_loader_window:978 - Data loader components initialized successfully
2025-06-07 10:27:12 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:27:14 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:27:14 [    INFO] src.gui.data_loader_window:2111 - Status: 正在获取API数据...
2025-06-07 10:27:18 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 10:27:18 [    INFO] src.gui.data_loader_window:2111 - Status: API数据获取完成
2025-06-07 10:27:19 [    INFO] src.gui.data_loader_window:1182 - Data preview updated successfully
2025-06-07 10:29:48 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 10:29:48 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 10:29:48 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 10:29:48 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 10:29:48 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 10:29:48 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:29:48 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:29:48 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:29:48 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:29:48 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:29:48 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:29:48 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 10:29:48 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 10:29:50 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-07 10:29:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:29:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:29:50 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:29:50 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:29:50 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-07 10:29:50 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-07 10:29:50 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:29:52 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:29:52 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:30:02 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 10:30:02 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取完成
2025-06-07 10:30:03 [    INFO] src.gui.data_loader_window:1353 - Data preview updated successfully
2025-06-07 10:30:26 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:30:47 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 10:30:47 [    INFO] src.data_processing.format_converter:103 - Converting API data to GZ analysis format
2025-06-07 10:30:47 [    INFO] src.data_processing.format_converter:130 - Successfully converted to GZ analysis format
2025-06-07 10:30:52 [    INFO] src.analysis_integration.gz_integration:235 - Starting pile analysis for pile: 512856130
2025-06-07 10:30:52 [    INFO] src.analysis_integration.gz_integration:408 - Results saved: ./work\gz_analysis_512856130_20250607_103052.txt
2025-06-07 10:30:52 [    INFO] src.analysis_integration.gz_integration:408 - Results saved: ./work\gz_analysis_512856130_20250607_103052.json
2025-06-07 10:30:52 [    INFO] src.analysis_integration.gz_integration:408 - Results saved: ./work\gz_analysis_512856130_20250607_103052.csv
2025-06-07 10:30:52 [    INFO] src.analysis_integration.gz_integration:278 - Analysis completed successfully for pile: 512856130
2025-06-07 10:30:59 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-07 10:30:59 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:30:59 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:30:59 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:30:59 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:30:59 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-07 10:30:59 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-07 10:30:59 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:31:01 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:31:01 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:31:10 [    INFO] src.data_acquisition.api_client:175 - Successfully fetched pile data for ID: 512856130
2025-06-07 10:31:10 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取完成
2025-06-07 10:31:10 [    INFO] src.gui.data_loader_window:1353 - Data preview updated successfully
2025-06-07 10:34:43 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 10:34:43 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 10:34:43 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 10:34:43 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 10:34:43 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 10:34:43 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:34:43 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:34:43 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:34:43 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:34:43 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:34:43 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:34:43 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 10:34:43 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 10:34:45 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-07 10:34:45 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:34:45 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:34:45 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:34:45 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:34:45 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-07 10:34:45 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-07 10:34:45 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:34:47 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:34:47 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:34:49 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30B8C20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:34:53 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30CC190>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:34:59 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30CC2D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:35:01 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB005F360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:35:01 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取失败: API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB005F360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:35:05 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:35:05 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:35:07 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB005F490>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:35:11 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30B1FD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:35:17 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB00C7AC0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:35:19 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB00C7CE0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:35:19 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取失败: API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB00C7CE0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:35:30 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-07 10:35:30 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:35:30 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:35:30 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:35:30 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:35:30 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-07 10:35:30 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-07 10:35:30 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:35:34 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:35:34 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:35:36 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D8AF0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:35:40 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D8C00>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:35:46 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D8E20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:35:48 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D9040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:35:48 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取失败: API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D9040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:36:01 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:36:01 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:36:03 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D9480>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:36:07 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D9590>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:36:13 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D97B0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:36:15 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D99D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:36:15 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取失败: API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D99D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:37:25 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 10:37:25 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 10:37:25 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 10:37:25 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 10:37:25 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 10:37:25 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:37:25 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:37:25 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:37:25 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:37:25 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:37:25 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:37:25 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 10:37:25 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 10:37:30 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-07 10:37:30 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:37:30 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:37:30 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:37:30 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:37:30 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-07 10:37:30 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-07 10:37:30 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:37:31 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:37:31 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:37:33 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017E106D8C20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:37:37 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017E106EC190>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:37:43 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017E106EC2D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:37:45 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017E0D6BF360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:37:45 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取失败: API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017E0D6BF360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:38:29 [    INFO] src.gui.data_loader_window:2284 - Status: 已选择文件数据源
2025-06-07 10:38:56 [    INFO] src.gui.data_loader_window:2284 - Status: 已选择文件: test_II_4.txt
2025-06-07 10:38:57 [    INFO] src.gui.data_loader_window:2284 - Status: 正在加载文件数据...
2025-06-07 10:38:57 [    INFO] src.gui.data_loader_window:2284 - Status: 文件数据加载失败: 不支持的文件格式: .txt
2025-06-07 10:39:27 [    INFO] src.utils.logger:100 - Logging system initialized
2025-06-07 10:39:27 [    INFO] src.utils.logger:101 - Log level: INFO
2025-06-07 10:39:27 [    INFO] src.utils.logger:102 - Log file: logs\pile_analysis_20250607.log
2025-06-07 10:39:27 [    INFO] src.utils.logger:103 - Console output: True
2025-06-07 10:39:27 [    INFO] __main__:211 - Starting Pile Analysis System
2025-06-07 10:39:27 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:39:27 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:39:27 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:39:27 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:39:27 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:39:27 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:39:27 [    INFO] src.analysis_integration.gz_integration:109 - Successfully loaded GZ analyzer class: GZTraditionalAnalyzer from d:\2025projects\alpiles_modified_final\analysis\gz_traditional_analysis.py
2025-06-07 10:39:27 [    INFO] src.gui.main_window:192 - System components initialized successfully
2025-06-07 10:39:31 [    INFO] src.gui.data_loader_window:2284 - Status: 正在初始化组件...
2025-06-07 10:39:31 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:39:31 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\api_config.json
2025-06-07 10:39:31 [    INFO] src.utils.config_manager:56 - ConfigManager initialized with config directory: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config
2025-06-07 10:39:31 [    INFO] src.utils.config_manager:113 - Loaded configuration from: d:\2025projects\alpiles_modified_final\analysis\pile_analysis_system\config\analysis_config.json
2025-06-07 10:39:31 [    INFO] src.gui.data_loader_window:2284 - Status: 组件初始化完成
2025-06-07 10:39:31 [    INFO] src.gui.data_loader_window:1149 - Data loader components initialized successfully
2025-06-07 10:39:31 [    INFO] src.gui.main_window:538 - Data loader window opened
2025-06-07 10:39:32 [    INFO] src.data_acquisition.api_client:143 - Fetching pile data for ID: 512856130
2025-06-07 10:39:32 [    INFO] src.gui.data_loader_window:2284 - Status: 正在获取API数据...
2025-06-07 10:39:34 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002693C048C20>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:39:38 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002693C05C190>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:39:44 [ WARNING] urllib3.connectionpool:868 - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002693C05C2D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': http://*************/pilediag/pile/pileJson
2025-06-07 10:39:46 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000026938FEF360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:39:46 [    INFO] src.gui.data_loader_window:2284 - Status: API数据获取失败: API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000026938FEF360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
