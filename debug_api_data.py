#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Debug API Data Structure
调试API数据结构

This script fetches real API data and analyzes its structure to understand
how to properly parse and export the data.
"""

import sys
import os
import json
import logging
from pprint import pprint

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data_acquisition.api_client import PileAPIClient
from src.utils.logger import setup_logging


def analyze_api_data():
    """分析API数据结构"""
    print("Fetching and analyzing API data structure...")
    
    try:
        # 设置日志
        setup_logging()
        
        # 初始化API客户端
        api_client = PileAPIClient()
        
        # 获取数据
        pile_id = "512856130"
        pile_data = api_client.get_pile_data(pile_id)
        
        print(f"Successfully fetched data for pile: {pile_data.get('Pile', 'Unknown')}")
        print("\n" + "="*60)
        print("API DATA STRUCTURE ANALYSIS")
        print("="*60)
        
        # 分析主要字段
        print("\n1. Main Fields:")
        for key, value in pile_data.items():
            if isinstance(value, (list, dict)):
                if isinstance(value, list):
                    print(f"  {key}: List with {len(value)} items")
                    if value and isinstance(value[0], dict):
                        print(f"    First item keys: {list(value[0].keys())}")
                else:
                    print(f"  {key}: Dict with keys: {list(value.keys())}")
            else:
                print(f"  {key}: {type(value).__name__} = {value}")
        
        # 分析Result_Sections_Data
        print("\n2. Result_Sections_Data Analysis:")
        result_sections = pile_data.get('Result_Sections_Data', [])
        if result_sections:
            for i, section in enumerate(result_sections):
                print(f"  Section {i}:")
                for key, value in section.items():
                    if isinstance(value, list):
                        print(f"    {key}: List with {len(value)} items")
                        if value and isinstance(value[0], dict):
                            print(f"      First item keys: {list(value[0].keys())}")
                    elif isinstance(value, dict):
                        print(f"    {key}: Dict with keys: {list(value.keys())}")
                    else:
                        print(f"    {key}: {value}")
        
        # 分析Test_Sections_Data
        print("\n3. Test_Sections_Data Analysis:")
        test_sections = pile_data.get('Test_Sections_Data', [])
        if test_sections:
            for i, section in enumerate(test_sections):
                print(f"  Test Section {i}:")
                for key, value in section.items():
                    if isinstance(value, list):
                        print(f"    {key}: List with {len(value)} items")
                        if value and isinstance(value[0], dict):
                            print(f"      First item keys: {list(value[0].keys())}")
                            # 分析UltraData
                            if key == 'UltraData' and value:
                                ultra_data = value[0]
                                print(f"      UltraData sample:")
                                for uk, uv in ultra_data.items():
                                    if isinstance(uv, list) and uk == 'WaveData':
                                        print(f"        {uk}: List with {len(uv)} wave points")
                                    else:
                                        print(f"        {uk}: {uv}")
                    elif isinstance(value, dict):
                        print(f"    {key}: Dict with keys: {list(value.keys())}")
                    else:
                        print(f"    {key}: {value}")
        
        # 保存完整数据到文件用于详细分析
        output_file = "api_data_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(pile_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n4. Full data saved to: {output_file}")
        print(f"   File size: {os.path.getsize(output_file)} bytes")
        
        # 关闭客户端
        api_client.close()
        
        return pile_data
        
    except Exception as e:
        print(f"Error analyzing API data: {str(e)}")
        return None


def main():
    """主函数"""
    pile_data = analyze_api_data()
    
    if pile_data:
        print("\n" + "="*60)
        print("ANALYSIS COMPLETE")
        print("="*60)
        print("Next steps:")
        print("1. Review the api_data_analysis.json file")
        print("2. Update kbz_exporter.py based on the actual data structure")
        print("3. Implement proper parsing for Result_Sections_Data and Test_Sections_Data")
        print("4. Add wave data and detailed measurement point information")
    else:
        print("Failed to analyze API data")


if __name__ == "__main__":
    main()
