2025-06-07 09:13:06 [   ERROR] src.gui.data_loader_window:511 - 更新数据预览失败: 'str' object has no attribute 'keys'
2025-06-07 09:15:27 [   ERROR] src.gui.data_loader_window:511 - 更新数据预览失败: 'str' object has no attribute 'keys'
2025-06-07 09:27:19 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://*************/pilediag/pile/pileJson
2025-06-07 10:05:01 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: 502 Server Error: Bad Gateway for url: http://*************/pilediag/pile/pileJson
2025-06-07 10:10:26 [   ERROR] src.gui.data_loader_window:557 - 截面分析失败: 'str' object has no attribute 'get'
2025-06-07 10:10:52 [   ERROR] src.gui.data_loader_window:1186 - 更新数据预览失败: invalid command name ".!toplevel.!frame.!labelframe2.!notebook.!frame4.!frame.!labelframe2.!notebook.!frame.!treeview"
2025-06-07 10:10:55 [   ERROR] src.gui.data_loader_window:1186 - 更新数据预览失败: invalid command name ".!toplevel.!frame.!labelframe2.!notebook.!frame4.!frame.!labelframe2.!notebook.!frame.!treeview"
2025-06-07 10:35:01 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB005F360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:35:19 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB00C7CE0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:35:48 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D9040>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:36:15 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000016EB30D99D0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:37:45 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000017E0D6BF360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
2025-06-07 10:39:46 [   ERROR] src.data_acquisition.api_client:180 - API request failed for pile 512856130: HTTPConnectionPool(host='127.0.0.1', port=7890): Max retries exceeded with url: http://*************/pilediag/pile/pileJson (Caused by ProxyError('Unable to connect to proxy', NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000026938FEF360>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')))
