# 简化桩基分析系统 - Simplified Pile Analysis System

## 概述 Overview

这是一个简化版的桩基分析系统，专门用于连接桩基检测API并导出类似KBZ1-9.TXT和KBZ1-9.xlsx格式的数据文件。

This is a simplified pile analysis system designed specifically for connecting to pile detection APIs and exporting data files similar to KBZ1-9.TXT and KBZ1-9.xlsx formats.

## 主要功能 Main Features

- ✅ 连接HTTP API接口获取桩基数据
- ✅ 导出TXT格式文件（类似KBZ1-9.TXT）
- ✅ 导出Excel格式文件（类似KBZ1-9.xlsx）
- ✅ 简化的图形用户界面
- ✅ 数据缓存功能
- ✅ 错误处理和日志记录

## API接口信息 API Information

- **接口地址**: http://*************/pilediag/pile/pileJson
- **请求方式**: POST
- **请求参数**: `{"id": "512856130"}`
- **返回格式**: `{"message": "请求成功", "code": "0", "data": {}}`

## 安装和运行 Installation and Usage

### 1. 安装依赖 Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. 运行简化系统 Run Simplified System

```bash
python simplified_main.py
```

### 3. 运行测试 Run Tests

```bash
python test_simplified_system.py
```

## 使用说明 Usage Instructions

### 图形界面使用 GUI Usage

1. 启动程序后，在"桩基ID"输入框中输入要查询的桩基ID
2. 选择输出目录（默认为当前目录）
3. 选择导出格式（TXT文件、Excel文件或两者）
4. 点击"获取并导出数据"按钮
5. 查看状态信息了解处理进度
6. 导出完成后，在指定目录查看生成的文件

### 命令行使用 Command Line Usage

```python
from src.data_acquisition.api_client import PileAPIClient
from src.data_processing.kbz_exporter import KBZExporter

# 初始化组件
api_client = PileAPIClient()
exporter = KBZExporter()

# 获取数据
pile_data = api_client.get_pile_data("512856130")

# 导出文件
txt_file = exporter.export_to_txt(pile_data, "./output", "KBZ1-9")
excel_file = exporter.export_to_excel(pile_data, "./output", "KBZ1-9")

# 清理
api_client.close()
```

## 文件结构 File Structure

```
simplified_main.py              # 简化主程序
test_simplified_system.py       # 测试脚本
src/
├── data_acquisition/
│   └── api_client.py           # API客户端
├── data_processing/
│   └── kbz_exporter.py         # KBZ格式导出器
└── utils/
    ├── config_manager.py       # 配置管理
    ├── logger.py              # 日志管理
    └── error_handler.py       # 错误处理
config/
└── api_config.json            # API配置文件
```

## 导出文件格式 Export File Formats

### TXT文件格式 TXT File Format

导出的TXT文件包含以下内容：
- 基础数据信息（桩基名称、强度等级、检测日期等）
- 各剖面的测量数据（深度、声时、声速、幅度、频率、PSD值）
- 统计分析数据（平均值、标准差、临界值等）
- 原始测试数据记录

### Excel文件格式 Excel File Format

导出的Excel文件包含三个工作表：
1. **桩信息表**: 工程基本信息和桩基参数
2. **数据表**: 各剖面的详细测量数据和统计分析
3. **单桩报告表**: 检测结果汇总和质量评定

## 配置说明 Configuration

### API配置 API Configuration

编辑 `config/api_config.json` 文件来修改API设置：

```json
{
  "api_endpoints": {
    "pile_data": {
      "url": "http://*************/pilediag/pile/pileJson",
      "method": "POST",
      "timeout": 30,
      "retry_attempts": 3
    }
  }
}
```

### 缓存设置 Cache Settings

```json
{
  "cache_settings": {
    "enabled": true,
    "cache_duration_minutes": 30,
    "cache_directory": "./cache"
  }
}
```

## 错误处理 Error Handling

系统包含完善的错误处理机制：
- API连接失败时的重试机制
- 数据验证和格式检查
- 详细的错误日志记录
- 用户友好的错误提示

## 日志记录 Logging

日志文件保存在 `logs/` 目录下：
- `pile_analysis_YYYYMMDD.log`: 一般日志
- `pile_analysis_errors_YYYYMMDD.log`: 错误日志

## 性能优化 Performance Optimization

- 数据缓存减少重复API调用
- 异步处理避免界面冻结
- 连接池提高网络效率
- 内存优化处理大数据量

## 故障排除 Troubleshooting

### 常见问题 Common Issues

1. **API连接失败**
   - 检查网络连接
   - 验证API地址和参数
   - 查看错误日志

2. **文件导出失败**
   - 检查输出目录权限
   - 确保磁盘空间充足
   - 验证数据格式

3. **界面无响应**
   - 等待数据处理完成
   - 检查系统资源使用情况
   - 重启应用程序

### 调试模式 Debug Mode

启用调试模式获取更详细的日志信息：

```bash
python simplified_main.py --log-level DEBUG
```

## 技术支持 Technical Support

如遇到问题，请：
1. 查看日志文件了解详细错误信息
2. 运行测试脚本验证系统功能
3. 检查配置文件设置
4. 联系技术支持团队

## 版本信息 Version Information

- 版本: 1.0.0
- 更新日期: 2024-12-23
- 兼容性: Python 3.8+

## 许可证 License

本软件仅供内部使用，请勿外传。
