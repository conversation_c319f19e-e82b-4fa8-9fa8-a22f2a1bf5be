#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Pile Analysis System - Main Entry Point
桩基分析系统主程序

This is the main entry point for the pile analysis system that integrates
API data acquisition, processing, and GZ traditional analysis.
"""

import sys
import os
import logging
import argparse
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.gui.main_window import PileAnalysisMainWindow
from src.utils.logger import setup_logging
from src.utils.config_manager import ConfigManager


def setup_environment():
    """设置运行环境"""
    # 创建必要的目录
    directories = ['logs', 'cache', 'temp', 'work', 'results']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # 设置日志
    setup_logging()


def run_gui_mode():
    """运行GUI模式"""
    import tkinter as tk
    from tkinter import messagebox
    
    try:
        root = tk.Tk()
        app = PileAnalysisMainWindow(root)
        
        # 设置窗口图标（如果存在）
        try:
            root.iconbitmap('assets/icon.ico')
        except:
            pass
        
        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f'{width}x{height}+{x}+{y}')
        
        root.mainloop()
        
    except Exception as e:
        logging.error(f"Failed to start GUI: {str(e)}")
        if 'root' in locals():
            messagebox.showerror("错误", f"启动GUI失败: {str(e)}")
        else:
            print(f"Error: Failed to start GUI: {str(e)}")
        return 1
    
    return 0


def run_cli_mode(args):
    """运行命令行模式"""
    from src.data_acquisition.api_client import PileAPIClient
    from src.data_processing.format_converter import PileDataConverter
    from src.analysis_integration.gz_integration import GZAnalysisIntegrator
    
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Starting CLI mode analysis")
        
        # 初始化组件
        api_client = PileAPIClient()
        converter = PileDataConverter()
        integrator = GZAnalysisIntegrator()
        
        # 获取桩基数据
        if args.pile_ids:
            pile_ids = args.pile_ids.split(',')
        else:
            pile_ids = ['512856130']  # 默认测试ID
        
        results = []
        
        for pile_id in pile_ids:
            try:
                logger.info(f"Processing pile: {pile_id}")
                
                # 1. 获取API数据
                pile_data = api_client.get_pile_data(pile_id.strip())
                
                # 2. 转换格式
                gz_data = converter.convert_api_to_gz_format(pile_data)
                
                # 3. 运行分析
                analysis_result = integrator.analyze_pile_data(gz_data, pile_id.strip())
                
                results.append(analysis_result)
                logger.info(f"Successfully processed pile: {pile_id}")
                
            except Exception as e:
                logger.error(f"Failed to process pile {pile_id}: {str(e)}")
                results.append({
                    'pile_id': pile_id.strip(),
                    'success': False,
                    'error': str(e)
                })
        
        # 输出结果摘要
        successful = sum(1 for r in results if r.get('success', False))
        total = len(results)
        
        print(f"\nAnalysis Summary:")
        print(f"Total piles processed: {total}")
        print(f"Successful analyses: {successful}")
        print(f"Failed analyses: {total - successful}")
        
        if args.output:
            # 保存结果到文件
            import json
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"Results saved to: {args.output}")
        
        # 清理资源
        api_client.close()
        
        return 0 if successful == total else 1
        
    except Exception as e:
        logger.error(f"CLI mode failed: {str(e)}")
        print(f"Error: {str(e)}")
        return 1


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Pile Analysis System - 桩基分析系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                           # 启动GUI模式
  python main.py --cli --pile-ids 512856130   # 命令行模式分析单个桩
  python main.py --cli --pile-ids "123,456,789" --output results.json  # 批量分析
        """
    )
    
    parser.add_argument(
        '--cli', 
        action='store_true',
        help='运行命令行模式（默认为GUI模式）'
    )
    
    parser.add_argument(
        '--pile-ids',
        type=str,
        help='桩基ID列表，用逗号分隔（CLI模式）'
    )
    
    parser.add_argument(
        '--output',
        type=str,
        help='输出文件路径（CLI模式）'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别'
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version='Pile Analysis System v1.0.0'
    )
    
    args = parser.parse_args()
    
    # 设置环境
    setup_environment()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # 设置配置文件路径
    if args.config:
        os.environ['PILE_ANALYSIS_CONFIG_PATH'] = args.config
    
    logger = logging.getLogger(__name__)
    logger.info("Starting Pile Analysis System")
    
    try:
        if args.cli:
            # 命令行模式
            return run_cli_mode(args)
        else:
            # GUI模式
            return run_gui_mode()
            
    except KeyboardInterrupt:
        logger.info("Program interrupted by user")
        print("\nProgram interrupted by user")
        return 0
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        print(f"Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
